#!/usr/bin/env python3
import csv
import sys
import os
from datetime import datetime

# This script will process the transaction data and add Euro prices
# based on historical exchange rates

# Sample data structure to hold our transactions
transactions = []

# Read the transaction data from the CSV file
with open('/Users/<USER>/Desktop/MAK-Files/Transactions_2019.csv', 'r') as file:
    reader = csv.reader(file, delimiter=';')
    header = next(reader)
    
    for row in reader:
        if len(row) >= 10 and row[5] == "Trade" and row[6] and row[8]:
            # Extract the date in the format DD.MM.YYYY
            date_str = row[1].split(' ')[0]
            
            # Extract other relevant information
            outgoing_asset = row[6]
            outgoing_amount = float(row[7].replace(',', '.')) if row[7] else 0
            incoming_asset = row[8]
            incoming_amount = float(row[9].replace(',', '.')) if row[9] else 0
            
            # Calculate price per coin in the original currency
            price_per_coin = outgoing_amount / incoming_amount if incoming_amount > 0 else 0
            
            transactions.append({
                'date': date_str,
                'outgoing_asset': outgoing_asset,
                'outgoing_amount': outgoing_amount,
                'incoming_asset': incoming_asset,
                'incoming_amount': incoming_amount,
                'price_per_coin': price_per_coin
            })

# Historical prices for ETH and BNB in EUR for 2019
# These are approximate monthly averages and would need to be replaced with actual daily prices
# Format: 'MM.YYYY': price_in_eur
eth_eur_prices = {
    '02.2019': 120.0,
    '03.2019': 135.0,
    '04.2019': 150.0,
    '05.2019': 220.0,
    '06.2019': 270.0,
    '08.2019': 190.0,
    '09.2019': 180.0,
    '10.2019': 175.0,
    '12.2019': 150.0
}

bnb_eur_prices = {
    '02.2019': 8.5,
    '03.2019': 13.0,
    '04.2019': 20.0,
    '05.2019': 28.0,
    '06.2019': 32.0,
    '08.2019': 25.0,
    '09.2019': 20.0,
    '10.2019': 18.0,
    '12.2019': 14.0
}

# Add Euro prices to each transaction
for tx in transactions:
    date_parts = tx['date'].split('.')
    month_year = f"{date_parts[1]}.{date_parts[2]}"
    
    if tx['outgoing_asset'] == 'ETH':
        if month_year in eth_eur_prices:
            eth_price = eth_eur_prices[month_year]
            tx['eth_eur_price'] = eth_price
            tx['cost_in_eur'] = tx['outgoing_amount'] * eth_price
            tx['price_per_coin_eur'] = tx['cost_in_eur'] / tx['incoming_amount'] if tx['incoming_amount'] > 0 else 0
    
    elif tx['outgoing_asset'] == 'BNB':
        if month_year in bnb_eur_prices:
            bnb_price = bnb_eur_prices[month_year]
            tx['bnb_eur_price'] = bnb_price
            tx['cost_in_eur'] = tx['outgoing_amount'] * bnb_price
            tx['price_per_coin_eur'] = tx['cost_in_eur'] / tx['incoming_amount'] if tx['incoming_amount'] > 0 else 0

# Print the results
print("Date,Outgoing Asset,Outgoing Amount,Incoming Asset,Incoming Amount,Price,Cost in EUR,Price per Coin EUR")
for tx in transactions:
    print(f"{tx['date']},{tx['outgoing_asset']},{tx['outgoing_amount']},{tx['incoming_asset']},{tx['incoming_amount']},{tx['price_per_coin']},{tx.get('cost_in_eur', 'N/A')},{tx.get('price_per_coin_eur', 'N/A')}")

# Note: This script uses approximate monthly average prices.
# For accurate historical daily prices, you would need to use a cryptocurrency price API
# or a historical price dataset.

{"version": 3, "file": "SubscriptionManager.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/session/SubscriptionManager.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAGH,OAAO,EAEL,YAAY,EACZ,wBAAwB,GACzB,MAAM,+BAA+B,CAAC;AAIvC,OAAO,EAAC,UAAU,EAAE,oBAAoB,EAAC,MAAM,aAAa,CAAC;AAE7D;;;;;GAKG;AACH,MAAM,UAAU,gBAAgB,CAAC,GAAG,CAAU;IAC5C,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAY,EAAE,CAAY,EAAE,EAAE,CAC7C,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAC9C,CAAC;AACJ,CAAC;AAED,qDAAqD;AACrD,MAAM,UAAU,YAAY,CAC1B,MAAiC;IAEjC,MAAM,SAAS,GAAG,IAAI,GAAG,EAA2B,CAAC;IAErD,SAAS,SAAS,CAAC,MAAiC;QAClD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAED,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QAC3B,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,YAAY,CAAC,UAAU,CAAC,SAAS;gBACpC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC5D,MAAM;YACR,KAAK,YAAY,CAAC,UAAU,CAAC,eAAe;gBAC1C,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC;gBAClE,MAAM;YACR,KAAK,YAAY,CAAC,UAAU,CAAC,GAAG;gBAC9B,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;gBACtD,MAAM;YACR,KAAK,YAAY,CAAC,UAAU,CAAC,OAAO;gBAClC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC1D,MAAM;YACR,KAAK,YAAY,CAAC,UAAU,CAAC,MAAM;gBACjC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;gBACzD,MAAM;YACR;gBACE,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;IACH,CAAC;IAED,OAAO,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;AACjC,CAAC;AAED,MAAM,OAAO,mBAAmB;IAC9B,qBAAqB,GAAG,CAAC,CAAC;IAC1B,yEAAyE;IACzE,qBAAqB;IACrB,qDAAqD;IACrD,2BAA2B,GAAG,IAAI,GAAG,EAMlC,CAAC;IACJ,uBAAuB,CAAyB;IAEhD,YAAY,sBAA8C;QACxD,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;IACxD,CAAC;IAED,4BAA4B,CAC1B,WAAoC,EACpC,SAAiD;QAEjD,MAAM,qBAAqB,GAAG,KAAK,CAAC,IAAI,CACtC,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE,CACxC;aACE,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YACjB,QAAQ,EAAE,IAAI,CAAC,uCAAuC,CACpD,WAAW,EACX,SAAS,EACT,OAAO,CACR;YACD,OAAO;SACR,CAAC,CAAC;aACF,MAAM,CAAC,CAAC,EAAC,QAAQ,EAAC,EAAE,EAAE,CAAC,QAAQ,KAAK,IAAI,CAGxC,CAAC;QAEJ,6BAA6B;QAC7B,OAAO,qBAAqB;aACzB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;aACvC,GAAG,CAAC,CAAC,EAAC,OAAO,EAAC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;IAED,uCAAuC,CACrC,WAAoC,EACpC,SAAiD,EACjD,OAAwB;QAExB,MAAM,iBAAiB,GAAG,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACxE,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,sBAAsB,GAC1B,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;QAEhE,qCAAqC;QACrC,MAAM,gBAAgB,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,CAAC,CAAC,CAAC,CAAC;QAEtE,uCAAuC;QACvC,MAAM,UAAU,GAAa,gBAAgB;aAC1C,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACf,wCAAwC;YACxC,MAAM,QAAQ,GAAG,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC,WAAW,CAAC,CAAC;YAClE,gEAAgE;YAChE,0BAA0B;YAC1B,gEAAgE;YAChE,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC5B,MAAM,WAAW,GAAG,iBAAiB;qBAClC,GAAG,CAAC,OAAO,CAAC;oBACb,EAAE,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;gBACrC,iEAAiE;gBACjE,sDAAsD;gBACtD,OAAO,QAAQ,IAAI,WAAW;oBAC5B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC;oBACjC,CAAC,CAAC,gDAAgD;wBAChD,yBAAyB;wBACzB,CAAC,QAAQ,IAAI,WAAW,CAAC,CAAC;YAChC,CAAC;YACD,iEAAiE;YACjE,IAAI,oBAAoB,CAAC,WAAW,CAAC,EAAE,CAAC;gBACtC,MAAM,WAAW,GAAG,iBAAiB;qBAClC,GAAG,CAAC,OAAO,CAAC;oBACb,EAAE,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;gBAC/C,iEAAiE;gBACjE,sDAAsD;gBACtD,OAAO,QAAQ,IAAI,WAAW;oBAC5B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC;oBACjC,CAAC,CAAC,gDAAgD;wBAChD,yBAAyB;wBACzB,CAAC,QAAQ,IAAI,WAAW,CAAC,CAAC;YAChC,CAAC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC;aACD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC;QAElC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,+BAA+B;YAC/B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,2BAA2B;QAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC;IACjC,CAAC;IAED;;;;;OAKG;IACH,cAAc,CACZ,aAAsC,EACtC,YAAoD,IAAI;QAExD,MAAM,eAAe,GACnB,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;QAEhE,KAAK,MAAM,wBAAwB,IAAI,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,EAAE,CAAC;YACjF,KAAK,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,wBAAwB,CAAC,OAAO,EAAE,EAAE,CAAC;gBAChE,6CAA6C;gBAC7C,IAAI,eAAe,KAAK,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;oBAC1C,SAAS;gBACX,CAAC;gBAED,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC;oBACpC,wCAAwC;oBACxC,kCAAkC;oBAClC;oBACE,8BAA8B;oBAC9B,KAAK,KAAK,aAAa;wBACvB,8BAA8B;wBAC9B,KAAK,KAAK,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBACxC,iDAAiD;wBACjD,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,aAAa,EACxC,CAAC;wBACD,OAAO,IAAI,CAAC;oBACd,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;;;;OASG;IACH,SAAS,CACP,KAA8B,EAC9B,SAAiD,EACjD,OAAwB;QAExB,+DAA+D;QAC/D,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;QAE1E,8CAA8C;QAC9C,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,YAAY,CAAC,UAAU,CAAC,eAAe;gBAC1C,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,eAAe,CAAC,UAAU,CAAC;qBAC1D,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CACrB,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,SAAS,EAAE,OAAO,CAAC,CAClD;qBACA,IAAI,EAAE,CAAC;YACZ,KAAK,YAAY,CAAC,UAAU,CAAC,GAAG;gBAC9B,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC;qBAC9C,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CACrB,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,SAAS,EAAE,OAAO,CAAC,CAClD;qBACA,IAAI,EAAE,CAAC;YACZ,KAAK,YAAY,CAAC,UAAU,CAAC,OAAO;gBAClC,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC;qBAClD,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CACrB,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,SAAS,EAAE,OAAO,CAAC,CAClD;qBACA,IAAI,EAAE,CAAC;YACZ,KAAK,YAAY,CAAC,UAAU,CAAC,MAAM;gBACjC,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC;qBACjD,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CACrB,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,SAAS,EAAE,OAAO,CAAC,CAClD;qBACA,IAAI,EAAE,CAAC;YACZ,KAAK,YAAY,CAAC,UAAU,CAAC,SAAS;gBACpC,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC;qBACpD,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CACrB,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,SAAS,EAAE,OAAO,CAAC,CAClD;qBACA,IAAI,EAAE,CAAC;YACZ,QAAQ;YACR,4BAA4B;QAC9B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACnD,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAC3D,CAAC;QACD,MAAM,iBAAiB,GAAG,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;QAEzE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACtC,iBAAiB,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAC9C,CAAC;QACD,MAAM,QAAQ,GAAG,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;QAEnD,MAAM,kBAAkB,GAAG,CACzB,SAAS,KAAK,IAAI;YAChB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YACrE,CAAC,CAAC,CAAC,SAAS,CAAC,CAChB;YACC,iFAAiF;YACjF,sBAAsB;aACrB,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC;QAEjE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,wDAAwD;YACxD,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,kBAAkB,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAC5C,KAAK;YACL,SAAS;SACV,CAAC,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACH,cAAc,CACZ,MAAiC,EACjC,UAAsD,EACtD,OAAwB;QAExB,iCAAiC;QACjC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;gBACvB,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,MAAM,iBAAiB,GAGjB,gBAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,UAAU,CAAC,CAAC;QAEzD,wCAAwC;QACxC,+EAA+E;QAC/E,iBAAiB;aACd,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,EAAE,CAC1B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAClD;aACA,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACH,WAAW,CACT,SAAkC,EAClC,SAAiD,EACjD,OAAwB;QAExB,IAAI,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC;IAED,iBAAiB,CACf,KAA8B,EAC9B,SAAiD,EACjD,OAAwB;QAExB,+DAA+D;QAC/D,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;QAE1E,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,wBAAwB,CAChC,2BAA2B,KAAK,KAC9B,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAChC,0BAA0B,CAC3B,CAAC;QACJ,CAAC;QACD,MAAM,iBAAiB,GAAG,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;QAEzE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,wBAAwB,CAChC,2BAA2B,KAAK,KAC9B,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAChC,0BAA0B,CAC3B,CAAC;QACJ,CAAC;QACD,MAAM,QAAQ,GAAG,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;QAEnD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,MAAM,IAAI,wBAAwB,CAChC,2BAA2B,KAAK,KAC9B,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAChC,0BAA0B,CAC3B,CAAC;QACJ,CAAC;QAED,OAAO,GAAG,EAAE;YACV,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEvB,0BAA0B;YAC1B,IAAI,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBACxB,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAClC,CAAC;YACD,IAAI,iBAAiB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBACjC,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACnD,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;CACF"}
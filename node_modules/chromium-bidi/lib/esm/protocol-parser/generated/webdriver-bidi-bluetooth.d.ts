/**
 * Copyright 2024 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * THIS FILE IS AUTOGENERATED by cddlconv 0.1.5.
 * Run `node tools/generate-bidi-types.mjs` to regenerate.
 * @see https://github.com/w3c/webdriver-bidi/blob/master/index.bs
 */
import z from 'zod';
export declare namespace Bluetooth {
    const BluetoothServiceUuidSchema: z.ZodLazy<z.ZodString>;
}
export declare namespace Bluetooth {
    const BluetoothManufacturerDataSchema: z.Zod<PERSON>azy<z.ZodObject<{
        key: z.ZodNumber;
        data: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        key: number;
        data: string;
    }, {
        key: number;
        data: string;
    }>>;
}
export declare namespace Bluetooth {
    const RequestDeviceSchema: z.ZodLazy<z.ZodString>;
}
export declare namespace Bluetooth {
    const RequestDeviceInfoSchema: z.ZodLazy<z.ZodObject<{
        id: z.ZodLazy<z.ZodString>;
        name: z.ZodUnion<[z.ZodString, z.ZodNull]>;
    }, "strip", z.ZodTypeAny, {
        name: string | null;
        id: string;
    }, {
        name: string | null;
        id: string;
    }>>;
}
export declare namespace Bluetooth {
    const RequestDevicePromptSchema: z.ZodLazy<z.ZodString>;
}
export declare namespace Bluetooth {
    const ScanRecordSchema: z.ZodLazy<z.ZodObject<{
        name: z.ZodOptional<z.ZodString>;
        uuids: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodString>, "many">>;
        appearance: z.ZodOptional<z.ZodNumber>;
        manufacturerData: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodObject<{
            key: z.ZodNumber;
            data: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            key: number;
            data: string;
        }, {
            key: number;
            data: string;
        }>>, "many">>;
    }, "strip", z.ZodTypeAny, {
        name?: string | undefined;
        uuids?: string[] | undefined;
        appearance?: number | undefined;
        manufacturerData?: {
            key: number;
            data: string;
        }[] | undefined;
    }, {
        name?: string | undefined;
        uuids?: string[] | undefined;
        appearance?: number | undefined;
        manufacturerData?: {
            key: number;
            data: string;
        }[] | undefined;
    }>>;
}
export declare namespace Bluetooth {
    const HandleRequestDevicePromptSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.handleRequestDevicePrompt">;
        params: z.ZodLazy<z.ZodIntersection<z.ZodObject<{
            context: z.ZodString;
            prompt: z.ZodLazy<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            prompt: string;
            context: string;
        }, {
            prompt: string;
            context: string;
        }>, z.ZodUnion<[z.ZodLazy<z.ZodObject<{
            accept: z.ZodLiteral<true>;
            device: z.ZodLazy<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            accept: true;
            device: string;
        }, {
            accept: true;
            device: string;
        }>>, z.ZodLazy<z.ZodObject<{
            accept: z.ZodLiteral<false>;
        }, "strip", z.ZodTypeAny, {
            accept: false;
        }, {
            accept: false;
        }>>]>>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            prompt: string;
            context: string;
        } & ({
            accept: true;
            device: string;
        } | {
            accept: false;
        });
        method: "bluetooth.handleRequestDevicePrompt";
    }, {
        params: {
            prompt: string;
            context: string;
        } & ({
            accept: true;
            device: string;
        } | {
            accept: false;
        });
        method: "bluetooth.handleRequestDevicePrompt";
    }>>;
}
export declare namespace Bluetooth {
    const HandleRequestDevicePromptParametersSchema: z.ZodLazy<z.ZodIntersection<z.ZodObject<{
        context: z.ZodString;
        prompt: z.ZodLazy<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        prompt: string;
        context: string;
    }, {
        prompt: string;
        context: string;
    }>, z.ZodUnion<[z.ZodLazy<z.ZodObject<{
        accept: z.ZodLiteral<true>;
        device: z.ZodLazy<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        accept: true;
        device: string;
    }, {
        accept: true;
        device: string;
    }>>, z.ZodLazy<z.ZodObject<{
        accept: z.ZodLiteral<false>;
    }, "strip", z.ZodTypeAny, {
        accept: false;
    }, {
        accept: false;
    }>>]>>>;
}
export declare namespace Bluetooth {
    const HandleRequestDevicePromptAcceptParametersSchema: z.ZodLazy<z.ZodObject<{
        accept: z.ZodLiteral<true>;
        device: z.ZodLazy<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        accept: true;
        device: string;
    }, {
        accept: true;
        device: string;
    }>>;
}
export declare namespace Bluetooth {
    const HandleRequestDevicePromptCancelParametersSchema: z.ZodLazy<z.ZodObject<{
        accept: z.ZodLiteral<false>;
    }, "strip", z.ZodTypeAny, {
        accept: false;
    }, {
        accept: false;
    }>>;
}
export declare namespace Bluetooth {
    const SimulateAdapterSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.simulateAdapter">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            state: z.ZodEnum<["absent", "powered-off", "powered-on"]>;
        }, "strip", z.ZodTypeAny, {
            context: string;
            state: "absent" | "powered-off" | "powered-on";
        }, {
            context: string;
            state: "absent" | "powered-off" | "powered-on";
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            context: string;
            state: "absent" | "powered-off" | "powered-on";
        };
        method: "bluetooth.simulateAdapter";
    }, {
        params: {
            context: string;
            state: "absent" | "powered-off" | "powered-on";
        };
        method: "bluetooth.simulateAdapter";
    }>>;
}
export declare namespace Bluetooth {
    const SimulateAdapterParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        state: z.ZodEnum<["absent", "powered-off", "powered-on"]>;
    }, "strip", z.ZodTypeAny, {
        context: string;
        state: "absent" | "powered-off" | "powered-on";
    }, {
        context: string;
        state: "absent" | "powered-off" | "powered-on";
    }>>;
}
export declare namespace Bluetooth {
    const SimulatePreconnectedPeripheralSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.simulatePreconnectedPeripheral">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            address: z.ZodString;
            name: z.ZodString;
            manufacturerData: z.ZodArray<z.ZodLazy<z.ZodObject<{
                key: z.ZodNumber;
                data: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                key: number;
                data: string;
            }, {
                key: number;
                data: string;
            }>>, "many">;
            knownServiceUuids: z.ZodArray<z.ZodLazy<z.ZodString>, "many">;
        }, "strip", z.ZodTypeAny, {
            context: string;
            name: string;
            manufacturerData: {
                key: number;
                data: string;
            }[];
            address: string;
            knownServiceUuids: string[];
        }, {
            context: string;
            name: string;
            manufacturerData: {
                key: number;
                data: string;
            }[];
            address: string;
            knownServiceUuids: string[];
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            context: string;
            name: string;
            manufacturerData: {
                key: number;
                data: string;
            }[];
            address: string;
            knownServiceUuids: string[];
        };
        method: "bluetooth.simulatePreconnectedPeripheral";
    }, {
        params: {
            context: string;
            name: string;
            manufacturerData: {
                key: number;
                data: string;
            }[];
            address: string;
            knownServiceUuids: string[];
        };
        method: "bluetooth.simulatePreconnectedPeripheral";
    }>>;
}
export declare namespace Bluetooth {
    const SimulatePreconnectedPeripheralParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        address: z.ZodString;
        name: z.ZodString;
        manufacturerData: z.ZodArray<z.ZodLazy<z.ZodObject<{
            key: z.ZodNumber;
            data: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            key: number;
            data: string;
        }, {
            key: number;
            data: string;
        }>>, "many">;
        knownServiceUuids: z.ZodArray<z.ZodLazy<z.ZodString>, "many">;
    }, "strip", z.ZodTypeAny, {
        context: string;
        name: string;
        manufacturerData: {
            key: number;
            data: string;
        }[];
        address: string;
        knownServiceUuids: string[];
    }, {
        context: string;
        name: string;
        manufacturerData: {
            key: number;
            data: string;
        }[];
        address: string;
        knownServiceUuids: string[];
    }>>;
}
export declare namespace Bluetooth {
    const SimulateAdvertisementSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.simulateAdvertisement">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            scanEntry: z.ZodLazy<z.ZodObject<{
                deviceAddress: z.ZodString;
                rssi: z.ZodNumber;
                scanRecord: z.ZodLazy<z.ZodObject<{
                    name: z.ZodOptional<z.ZodString>;
                    uuids: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodString>, "many">>;
                    appearance: z.ZodOptional<z.ZodNumber>;
                    manufacturerData: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodObject<{
                        key: z.ZodNumber;
                        data: z.ZodString;
                    }, "strip", z.ZodTypeAny, {
                        key: number;
                        data: string;
                    }, {
                        key: number;
                        data: string;
                    }>>, "many">>;
                }, "strip", z.ZodTypeAny, {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                }, {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                }>>;
            }, "strip", z.ZodTypeAny, {
                deviceAddress: string;
                rssi: number;
                scanRecord: {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                };
            }, {
                deviceAddress: string;
                rssi: number;
                scanRecord: {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                };
            }>>;
        }, "strip", z.ZodTypeAny, {
            context: string;
            scanEntry: {
                deviceAddress: string;
                rssi: number;
                scanRecord: {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                };
            };
        }, {
            context: string;
            scanEntry: {
                deviceAddress: string;
                rssi: number;
                scanRecord: {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                };
            };
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            context: string;
            scanEntry: {
                deviceAddress: string;
                rssi: number;
                scanRecord: {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                };
            };
        };
        method: "bluetooth.simulateAdvertisement";
    }, {
        params: {
            context: string;
            scanEntry: {
                deviceAddress: string;
                rssi: number;
                scanRecord: {
                    name?: string | undefined;
                    uuids?: string[] | undefined;
                    appearance?: number | undefined;
                    manufacturerData?: {
                        key: number;
                        data: string;
                    }[] | undefined;
                };
            };
        };
        method: "bluetooth.simulateAdvertisement";
    }>>;
}
export declare namespace Bluetooth {
    const SimulateAdvertisementParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        scanEntry: z.ZodLazy<z.ZodObject<{
            deviceAddress: z.ZodString;
            rssi: z.ZodNumber;
            scanRecord: z.ZodLazy<z.ZodObject<{
                name: z.ZodOptional<z.ZodString>;
                uuids: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodString>, "many">>;
                appearance: z.ZodOptional<z.ZodNumber>;
                manufacturerData: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodObject<{
                    key: z.ZodNumber;
                    data: z.ZodString;
                }, "strip", z.ZodTypeAny, {
                    key: number;
                    data: string;
                }, {
                    key: number;
                    data: string;
                }>>, "many">>;
            }, "strip", z.ZodTypeAny, {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            }, {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            }>>;
        }, "strip", z.ZodTypeAny, {
            deviceAddress: string;
            rssi: number;
            scanRecord: {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            };
        }, {
            deviceAddress: string;
            rssi: number;
            scanRecord: {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            };
        }>>;
    }, "strip", z.ZodTypeAny, {
        context: string;
        scanEntry: {
            deviceAddress: string;
            rssi: number;
            scanRecord: {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            };
        };
    }, {
        context: string;
        scanEntry: {
            deviceAddress: string;
            rssi: number;
            scanRecord: {
                name?: string | undefined;
                uuids?: string[] | undefined;
                appearance?: number | undefined;
                manufacturerData?: {
                    key: number;
                    data: string;
                }[] | undefined;
            };
        };
    }>>;
}
export declare namespace Bluetooth {
    const SimulateAdvertisementScanEntryParametersSchema: z.ZodLazy<z.ZodObject<{
        deviceAddress: z.ZodString;
        rssi: z.ZodNumber;
        scanRecord: z.ZodLazy<z.ZodObject<{
            name: z.ZodOptional<z.ZodString>;
            uuids: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodString>, "many">>;
            appearance: z.ZodOptional<z.ZodNumber>;
            manufacturerData: z.ZodOptional<z.ZodArray<z.ZodLazy<z.ZodObject<{
                key: z.ZodNumber;
                data: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                key: number;
                data: string;
            }, {
                key: number;
                data: string;
            }>>, "many">>;
        }, "strip", z.ZodTypeAny, {
            name?: string | undefined;
            uuids?: string[] | undefined;
            appearance?: number | undefined;
            manufacturerData?: {
                key: number;
                data: string;
            }[] | undefined;
        }, {
            name?: string | undefined;
            uuids?: string[] | undefined;
            appearance?: number | undefined;
            manufacturerData?: {
                key: number;
                data: string;
            }[] | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        deviceAddress: string;
        rssi: number;
        scanRecord: {
            name?: string | undefined;
            uuids?: string[] | undefined;
            appearance?: number | undefined;
            manufacturerData?: {
                key: number;
                data: string;
            }[] | undefined;
        };
    }, {
        deviceAddress: string;
        rssi: number;
        scanRecord: {
            name?: string | undefined;
            uuids?: string[] | undefined;
            appearance?: number | undefined;
            manufacturerData?: {
                key: number;
                data: string;
            }[] | undefined;
        };
    }>>;
}
export declare namespace Bluetooth {
    const RequestDevicePromptUpdatedSchema: z.ZodLazy<z.ZodObject<{
        method: z.ZodLiteral<"bluetooth.requestDevicePromptUpdated">;
        params: z.ZodLazy<z.ZodObject<{
            context: z.ZodString;
            prompt: z.ZodLazy<z.ZodString>;
            devices: z.ZodArray<z.ZodLazy<z.ZodObject<{
                id: z.ZodLazy<z.ZodString>;
                name: z.ZodUnion<[z.ZodString, z.ZodNull]>;
            }, "strip", z.ZodTypeAny, {
                name: string | null;
                id: string;
            }, {
                name: string | null;
                id: string;
            }>>, "many">;
        }, "strip", z.ZodTypeAny, {
            prompt: string;
            context: string;
            devices: {
                name: string | null;
                id: string;
            }[];
        }, {
            prompt: string;
            context: string;
            devices: {
                name: string | null;
                id: string;
            }[];
        }>>;
    }, "strip", z.ZodTypeAny, {
        params: {
            prompt: string;
            context: string;
            devices: {
                name: string | null;
                id: string;
            }[];
        };
        method: "bluetooth.requestDevicePromptUpdated";
    }, {
        params: {
            prompt: string;
            context: string;
            devices: {
                name: string | null;
                id: string;
            }[];
        };
        method: "bluetooth.requestDevicePromptUpdated";
    }>>;
}
export declare namespace Bluetooth {
    const RequestDevicePromptUpdatedParametersSchema: z.ZodLazy<z.ZodObject<{
        context: z.ZodString;
        prompt: z.ZodLazy<z.ZodString>;
        devices: z.ZodArray<z.ZodLazy<z.ZodObject<{
            id: z.ZodLazy<z.ZodString>;
            name: z.ZodUnion<[z.ZodString, z.ZodNull]>;
        }, "strip", z.ZodTypeAny, {
            name: string | null;
            id: string;
        }, {
            name: string | null;
            id: string;
        }>>, "many">;
    }, "strip", z.ZodTypeAny, {
        prompt: string;
        context: string;
        devices: {
            name: string | null;
            id: string;
        }[];
    }, {
        prompt: string;
        context: string;
        devices: {
            name: string | null;
            id: string;
        }[];
    }>>;
}

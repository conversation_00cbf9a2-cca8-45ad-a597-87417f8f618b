{"version": 3, "file": "module.js", "sourceRoot": "", "sources": ["../ts/module.ts"], "names": [], "mappings": ";;;AACA,mCAAkC;AAOlC,qCAAyE;AAOzE,yCAA4C;AAC5C,uCAA4D;AAC5D,mCAQgB;AA6BhB,MAAM,gCAAgC;IAKpC,YAAY,IAA+B;QACzC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAA;QACrC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAA;QAC3C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAA;QAC7C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAA;IAC7C,CAAC;CACF;AA6BD;;;;;;GAMG;AACH,MAAa,sBAAsB;IAKjC,YAAY,MAAoB;QAHxB,qBAAgB,GAAG,IAAI,GAAG,EAAsC,CAAA;QAChE,qBAAgB,GAAG,IAAI,GAAG,EAAsC,CAAA;QAuBhE,mBAAc,GAAG,CAAC,CAAA;QA0DlB,qBAAgB,GAAG,IAAI,gCAAgC,CAAC;YAC9D,YAAY,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,CAC3D,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACjC,IAAI;oBACF,MAAM,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;oBACzC,IAAI,CAAC,EAAE,EAAE;wBACP,MAAM,IAAI,KAAK,CAAC,wBAAwB,GAAG,oCAAoC,KAAK,GAAG,CAAC,CAAA;qBACzF;oBACD,OAAO,EAAE,CAAC,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;iBACzD;gBAAC,OAAO,KAAK,EAAE;oBACd,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAA;oBACzD,OAAO,CAAmB,CAAA;iBAC3B;YACH,CAAC,CAAC;YAEJ,eAAe,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,CAChC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACjC,IAAI;oBACF,MAAM,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;oBACxC,IAAI,CAAC,EAAE,EAAE;wBACP,MAAM,IAAI,KAAK,CAAC,uBAAuB,EAAE,6BAA6B,CAAC,CAAA;qBACxE;oBACD,OAAO,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,CAAA;iBAC9B;gBAAC,OAAO,KAAK,EAAE;oBACd,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAA;oBAC9D,OAAO,CAAC,CAAA;iBACT;YACH,CAAC,CAAC;YAEJ,gBAAgB,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE,CAClD,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACjC,IAAI;oBACF,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;oBACtD,IAAI,CAAC,gBAAgB,EAAE;wBACrB,MAAM,IAAI,KAAK,CAAC,uBAAuB,EAAE,iCAAiC,CAAC,CAAA;qBAC5E;oBAED,MAAM,UAAU,GAAG,gBAAgB,CAAC,gBAAgB,CAAA;oBACpD,IAAI,CAAC,UAAU,EAAE;wBACf,MAAM,IAAI,KAAK,CAAC,uBAAuB,EAAE,mCAAmC,CAAC,CAAA;qBAC9E;oBACD,OAAO,UAAU,CAAC,EAAE,EAAE,GAAG,EAAE,UAAU,CAAC,CAAA;iBACvC;gBAAC,OAAO,KAAK,EAAE;oBACd,OAAO,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAA;oBACvE,OAAO,CAA4B,CAAA;iBACpC;YACH,CAAC,CAAC;YAEJ,eAAe,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,GAAG,EAAE,cAAc,EAAE,UAAU,EAAE,EAAE,CACjE,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACjC,IAAI;oBACF,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;oBACtD,IAAI,CAAC,gBAAgB,EAAE;wBACrB,MAAM,IAAI,KAAK,CAAC,uBAAuB,EAAE,iCAAiC,CAAC,CAAA;qBAC5E;oBAED,MAAM,eAAe,GAAG,gBAAgB,CAAC,eAAe,CAAA;oBACxD,IAAI,CAAC,eAAe,EAAE;wBACpB,MAAM,IAAI,KAAK,CAAC,uBAAuB,EAAE,mCAAmC,CAAC,CAAA;qBAC9E;oBACD,OAAO,eAAe,CAAC,EAAE,EAAE,GAAG,EAAE,cAAc,EAAE,UAAU,CAAC,CAAA;iBAC5D;gBAAC,OAAO,KAAK,EAAE;oBACd,OAAO,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAA;oBACvE,OAAO,CAA4B,CAAA;iBACpC;YACH,CAAC,CAAC;SACL,CAAC,CAAA;QAhJA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAA;IAC/C,CAAC;IAED,mBAAmB,CAAC,EAAoB,EAAE,SAA2B;QACnE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,CAAC,CAAA;IAC1C,CAAC;IAED,aAAa,CAAC,EAAoB;QAChC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;IAClC,CAAC;IAED,mBAAmB,CAAC,GAAqB,EAAE,SAA2B;QACpE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,CAAA;IAC3C,CAAC;IAED,aAAa,CAAC,GAAqB;QACjC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;IACnC,CAAC;IAKO,cAAc,CACpB,QAA8B,EAC9B,EAAwB;QAExB,IAAI,QAAQ,EAAE;YACZ,+DAA+D;YAC/D,yEAAyE;YACzE,sEAAsE;YACtE,OAAO,QAAQ,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,EAAE;gBACnC,IAAI;oBACF,MAAM,MAAM,GAAG,EAAE,EAAE,CAAA;oBACnB,IAAI,CAAC,CAAC,MAAM,YAAY,OAAO,CAAC,EAAE;wBAChC,IAAA,gBAAQ,EAAC,uCAAuC,EAAE,MAAM,CAAC,CAAA;wBACzD,IAAI,CAAC,MAAM,CAAC,CAAA;wBACZ,OAAM;qBACP;oBAED,oCAAoC;oBACpC,IAAI,IAAI,CAAC,SAAS,EAAE;wBAClB,MAAM,IAAI,6BAAoB,CAC5B,yBAAyB,IAAI,CAAC,SAAS,CAAC,KAAK,4BAA4B,CAC1E,CAAA;qBACF;yBAAM;wBACL,IAAI,CAAC,SAAS,GAAG,IAAI,iCAAwB,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,CAAA;wBAC3E,IAAA,gBAAQ,EAAC,mCAAmC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;qBAC9D;oBAED,MAAM,CAAC,IAAI,CACT,CAAC,cAAc,EAAE,EAAE;wBACjB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;wBAC1B,IAAA,gBAAQ,EAAC,iCAAiC,EAAE,cAAc,CAAC,CAAA;wBAC3D,IAAI,CAAC,cAAc,CAAC,CAAA;oBACtB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;wBACR,IAAA,gBAAQ,EAAC,iCAAiC,EAAE,KAAK,CAAC,CAAA;wBAClD,OAAO,CAAC,KAAK,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAA;wBAC1E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;oBAC5B,CAAC,CACF,CAAA;iBACF;gBAAC,OAAO,KAAK,EAAE;oBACd,IAAA,gBAAQ,EAAC,8BAA8B,EAAE,KAAK,CAAC,CAAA;oBAC/C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;oBAC1B,MAAM,KAAK,CAAA;iBACZ;YACH,CAAC,CAAC,CAAA;SACH;QAED,kDAAkD;QAClD,MAAM,KAAK,GAAG,EAAE,EAAE,CAAA;QAClB,IAAI,KAAK,YAAY,OAAO,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAA;SAC/E;QACD,OAAO,KAAK,CAAA;IACd,CAAC;CAqEF;AAvJD,wDAuJC;AAED;;;GAGG;AACH,SAAgB,uBAAuB,CACrC,OAAuB,EACvB,OAA2B;IAE3B,IAAI,OAAO,CAAC,gBAAgB,EAAE;QAC5B,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAA;KACtD;IAED,IAAI,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE;QAC3C,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;KACnD;IAED,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,EAAE;QAC1C,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAA;KACjD;AACH,CAAC;AAfD,0DAeC;AAED;;;GAGG;AACH,SAAgB,6BAA6B,CAC3C,OAAU,EACV,OAA0B;IAE1B,IAAI,OAAO,CAAC,YAAY,EAAE;QACxB,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;KAC9C;IAED,IAAI,OAAO,CAAC,eAAe,EAAE;QAC3B,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;KACrD;IAED,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,EAAE;QAC1C,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAA;KACjD;IAED,IAAI,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE;QAC3C,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;KACnD;AACH,CAAC;AAnBD,sEAmBC;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,MAAa,iBAAiB;IAQ5B,eAAe;IACf,YAAY,MAAoB,EAAE,GAAc;QAC9C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,SAAS,GAAG,IAAI,sBAAsB,CAAC,MAAM,CAAC,CAAA;IACrD,CAAC;IAED;;;;OAIG;IACH,UAAU,CAAC,UAA0B,EAAE;QACrC,MAAM,EAAE,GAAG,IAAI,mBAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,SAAS,EAAE,CAAC,MAAM,EAAE,EAAE;YACvE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;YACpC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;QAClC,CAAC,CAAC,CAAA;QAEF,MAAM,OAAO,GAAG,IAAI,wBAAc,CAAC;YACjC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,EAAE;SACH,CAAC,CAAA;QAEF,uBAAuB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAEzC,IAAI,OAAO,CAAC,YAAY,EAAE;YACxB,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;SAC9C;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;;;OAIG;IACH,UAAU,CAAC,UAA0B,EAAE;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAA;QACjC,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC;YACjC,GAAG,OAAO;YACV,cAAc,EAAE,IAAA,cAAM,EAAC,OAAO,EAAE,OAAO,CAAC,cAAc,CAAC;SACxD,CAAC,CAAA;QACF,OAAO,CAAC,OAAO,GAAG,OAAO,CAAA;QACzB,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,QAAQ,CAAC,IAAY,EAAE,UAA6B,EAAE;QACpD,OAAO,gBAAK,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/B,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAA;YAE1C,6BAA6B,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;YAClD,MAAM,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;YAE3C,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,EAAE;gBAC1C,sEAAsE;gBACtE,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAA;aAC9B;YAED,IAAI,MAAM,CAAC,KAAK,EAAE;gBAChB,MAAM,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;gBACjD,MAAM,KAAK,CAAA;aACZ;YAED,MAAM,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;YACjD,OAAO,KAAK,CAAA;QACd,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;;;;;OAMG;IACH,MAAM;QACJ,OAAO,IAAI,CAAC,GAAG,CAAA;IACjB,CAAC;CACF;AAlHD,8CAkHC", "sourcesContent": ["import { QuickJSContext } from \"./context\"\nimport { debug<PERSON>og } from \"./debug\"\nimport {\n  Asyncify,\n  AsyncifySleepResult,\n  EitherModule,\n  EmscriptenModuleCallbacks,\n} from \"./emscripten-types\"\nimport { QuickJSAsyncifyError, QuickJSAsyncifySuspended } from \"./errors\"\nimport {\n  <PERSON><PERSON>ed<PERSON>eapCharPointer,\n  J<PERSON>ontextPointer,\n  JSRuntimePointer,\n  JSValuePointer,\n} from \"./types-ffi\"\nimport { Lifetime, Scope } from \"./lifetime\"\nimport { InterruptHandler, QuickJSRuntime } from \"./runtime\"\nimport {\n  AsyncRuntimeOptions,\n  concat,\n  ContextOptions,\n  EitherFFI,\n  JSModuleLoader,\n  RuntimeOptions,\n  RuntimeOptionsBase,\n} from \"./types\"\n\ntype EmscriptenCallback<BaseArgs extends any[], Result> = (\n  ...args: [Asyncify | undefined, ...BaseArgs]\n) => Result | AsyncifySleepResult<Result>\ntype MaybeAsyncEmscriptenCallback<T extends EmscriptenCallback<any, any>> =\n  T extends EmscriptenCallback<infer Args, infer Result>\n    ? (...args: Args) => Result | Promise<Result>\n    : never\ntype MaybeAsyncEmscriptenCallbacks = {\n  [K in keyof EmscriptenModuleCallbacks]: MaybeAsyncEmscriptenCallback<EmscriptenModuleCallbacks[K]>\n}\n\n/**\n * @private\n */\nexport interface ContextCallbacks {\n  callFunction: MaybeAsyncEmscriptenCallbacks[\"callFunction\"]\n}\n\n/**\n * @private\n */\nexport interface RuntimeCallbacks {\n  shouldInterrupt: MaybeAsyncEmscriptenCallbacks[\"shouldInterrupt\"]\n  loadModuleSource: MaybeAsyncEmscriptenCallbacks[\"loadModuleSource\"]\n  normalizeModule: MaybeAsyncEmscriptenCallbacks[\"normalizeModule\"]\n}\n\nclass QuickJSEmscriptenModuleCallbacks implements EmscriptenModuleCallbacks {\n  public callFunction: EmscriptenModuleCallbacks[\"callFunction\"]\n  public shouldInterrupt: EmscriptenModuleCallbacks[\"shouldInterrupt\"]\n  public loadModuleSource: EmscriptenModuleCallbacks[\"loadModuleSource\"]\n  public normalizeModule: EmscriptenModuleCallbacks[\"normalizeModule\"]\n  constructor(args: EmscriptenModuleCallbacks) {\n    this.callFunction = args.callFunction\n    this.shouldInterrupt = args.shouldInterrupt\n    this.loadModuleSource = args.loadModuleSource\n    this.normalizeModule = args.normalizeModule\n  }\n}\n\n/**\n * Options for [[QuickJSWASMModule.evalCode]].\n */\nexport interface ModuleEvalOptions {\n  /**\n   * Interrupt evaluation if `shouldInterrupt` returns `true`.\n   * See [[shouldInterruptAfterDeadline]].\n   */\n  shouldInterrupt?: InterruptHandler\n\n  /**\n   * Memory limit, in bytes, of WebAssembly heap memory used by the QuickJS VM.\n   */\n  memoryLimitBytes?: number\n\n  /**\n   * Stack size limit for this vm, in bytes\n   * To remove the limit, set to `0`.\n   */\n  maxStackSizeBytes?: number\n\n  /**\n   * Module loader for any `import` statements or expressions.\n   */\n  moduleLoader?: JSModuleLoader\n}\n\n/**\n * We use static functions per module to dispatch runtime or context calls from\n * C to the host.  This class manages the indirection from a specific runtime or\n * context pointer to the appropriate callback handler.\n *\n * @private\n */\nexport class QuickJSModuleCallbacks {\n  private module: EitherModule\n  private contextCallbacks = new Map<JSContextPointer, ContextCallbacks>()\n  private runtimeCallbacks = new Map<JSRuntimePointer, RuntimeCallbacks>()\n\n  constructor(module: EitherModule) {\n    this.module = module\n    this.module.callbacks = this.cToHostCallbacks\n  }\n\n  setRuntimeCallbacks(rt: JSRuntimePointer, callbacks: RuntimeCallbacks) {\n    this.runtimeCallbacks.set(rt, callbacks)\n  }\n\n  deleteRuntime(rt: JSRuntimePointer) {\n    this.runtimeCallbacks.delete(rt)\n  }\n\n  setContextCallbacks(ctx: JSContextPointer, callbacks: ContextCallbacks) {\n    this.contextCallbacks.set(ctx, callbacks)\n  }\n\n  deleteContext(ctx: JSContextPointer) {\n    this.contextCallbacks.delete(ctx)\n  }\n\n  private suspendedCount = 0\n  private suspended: QuickJSAsyncifySuspended | undefined\n\n  private handleAsyncify<T>(\n    asyncify: Asyncify | undefined,\n    fn: () => T | Promise<T>\n  ): T | AsyncifySleepResult<T> {\n    if (asyncify) {\n      // We must always call asyncify.handleSync around our function.\n      // This allows asyncify to resume suspended execution on the second call.\n      // Asyncify internally can detect sync behavior, and avoid suspending.\n      return asyncify.handleSleep((done) => {\n        try {\n          const result = fn()\n          if (!(result instanceof Promise)) {\n            debugLog(\"asyncify.handleSleep: not suspending:\", result)\n            done(result)\n            return\n          }\n\n          // Is promise, we intend to suspend.\n          if (this.suspended) {\n            throw new QuickJSAsyncifyError(\n              `Already suspended at: ${this.suspended.stack}\\nAttempted to suspend at:`\n            )\n          } else {\n            this.suspended = new QuickJSAsyncifySuspended(`(${this.suspendedCount++})`)\n            debugLog(\"asyncify.handleSleep: suspending:\", this.suspended)\n          }\n\n          result.then(\n            (resolvedResult) => {\n              this.suspended = undefined\n              debugLog(\"asyncify.handleSleep: resolved:\", resolvedResult)\n              done(resolvedResult)\n            },\n            (error) => {\n              debugLog(\"asyncify.handleSleep: rejected:\", error)\n              console.error(\"QuickJS: cannot handle error in suspended function\", error)\n              this.suspended = undefined\n            }\n          )\n        } catch (error) {\n          debugLog(\"asyncify.handleSleep: error:\", error)\n          this.suspended = undefined\n          throw error\n        }\n      })\n    }\n\n    // No asyncify - we should never return a promise.\n    const value = fn()\n    if (value instanceof Promise) {\n      throw new Error(\"Promise return value not supported in non-asyncify context.\")\n    }\n    return value\n  }\n\n  private cToHostCallbacks = new QuickJSEmscriptenModuleCallbacks({\n    callFunction: (asyncify, ctx, this_ptr, argc, argv, fn_id) =>\n      this.handleAsyncify(asyncify, () => {\n        try {\n          const vm = this.contextCallbacks.get(ctx)\n          if (!vm) {\n            throw new Error(`QuickJSContext(ctx = ${ctx}) not found for C function call \"${fn_id}\"`)\n          }\n          return vm.callFunction(ctx, this_ptr, argc, argv, fn_id)\n        } catch (error) {\n          console.error(\"[C to host error: returning null]\", error)\n          return 0 as JSValuePointer\n        }\n      }),\n\n    shouldInterrupt: (asyncify, rt) =>\n      this.handleAsyncify(asyncify, () => {\n        try {\n          const vm = this.runtimeCallbacks.get(rt)\n          if (!vm) {\n            throw new Error(`QuickJSRuntime(rt = ${rt}) not found for C interrupt`)\n          }\n          return vm.shouldInterrupt(rt)\n        } catch (error) {\n          console.error(\"[C to host interrupt: returning error]\", error)\n          return 1\n        }\n      }),\n\n    loadModuleSource: (asyncify, rt, ctx, moduleName) =>\n      this.handleAsyncify(asyncify, () => {\n        try {\n          const runtimeCallbacks = this.runtimeCallbacks.get(rt)\n          if (!runtimeCallbacks) {\n            throw new Error(`QuickJSRuntime(rt = ${rt}) not found for C module loader`)\n          }\n\n          const loadModule = runtimeCallbacks.loadModuleSource\n          if (!loadModule) {\n            throw new Error(`QuickJSRuntime(rt = ${rt}) does not support module loading`)\n          }\n          return loadModule(rt, ctx, moduleName)\n        } catch (error) {\n          console.error(\"[C to host module loader error: returning null]\", error)\n          return 0 as BorrowedHeapCharPointer\n        }\n      }),\n\n    normalizeModule: (asyncify, rt, ctx, moduleBaseName, moduleName) =>\n      this.handleAsyncify(asyncify, () => {\n        try {\n          const runtimeCallbacks = this.runtimeCallbacks.get(rt)\n          if (!runtimeCallbacks) {\n            throw new Error(`QuickJSRuntime(rt = ${rt}) not found for C module loader`)\n          }\n\n          const normalizeModule = runtimeCallbacks.normalizeModule\n          if (!normalizeModule) {\n            throw new Error(`QuickJSRuntime(rt = ${rt}) does not support module loading`)\n          }\n          return normalizeModule(rt, ctx, moduleBaseName, moduleName)\n        } catch (error) {\n          console.error(\"[C to host module loader error: returning null]\", error)\n          return 0 as BorrowedHeapCharPointer\n        }\n      }),\n  })\n}\n\n/**\n * Process RuntimeOptions and apply them to a QuickJSRuntime.\n * @private\n */\nexport function applyBaseRuntimeOptions(\n  runtime: QuickJSRuntime,\n  options: RuntimeOptionsBase\n): void {\n  if (options.interruptHandler) {\n    runtime.setInterruptHandler(options.interruptHandler)\n  }\n\n  if (options.maxStackSizeBytes !== undefined) {\n    runtime.setMaxStackSize(options.maxStackSizeBytes)\n  }\n\n  if (options.memoryLimitBytes !== undefined) {\n    runtime.setMemoryLimit(options.memoryLimitBytes)\n  }\n}\n\n/**\n * Process ModuleEvalOptions and apply them to a QuickJSRuntime.\n * @private\n */\nexport function applyModuleEvalRuntimeOptions<T extends QuickJSRuntime>(\n  runtime: T,\n  options: ModuleEvalOptions\n) {\n  if (options.moduleLoader) {\n    runtime.setModuleLoader(options.moduleLoader)\n  }\n\n  if (options.shouldInterrupt) {\n    runtime.setInterruptHandler(options.shouldInterrupt)\n  }\n\n  if (options.memoryLimitBytes !== undefined) {\n    runtime.setMemoryLimit(options.memoryLimitBytes)\n  }\n\n  if (options.maxStackSizeBytes !== undefined) {\n    runtime.setMaxStackSize(options.maxStackSizeBytes)\n  }\n}\n\n/**\n * This class presents a Javascript interface to QuickJS, a Javascript interpreter\n * that supports EcmaScript 2020 (ES2020).\n *\n * It wraps a single WebAssembly module containing the QuickJS library and\n * associated helper C code. WebAssembly modules are completely isolated from\n * each other by the host's WebAssembly runtime. Separate WebAssembly modules\n * have the most isolation guarantees possible with this library.\n *\n * The simplest way to start running code is {@link evalCode}. This shortcut\n * method will evaluate Javascript safely and return the result as a native\n * Javascript value.\n *\n * For more control over the execution environment, or to interact with values\n * inside QuickJS, create a context with {@link newContext} or a runtime with\n * {@link newRuntime}.\n */\nexport class QuickJSWASMModule {\n  /** @private */\n  protected ffi: EitherFFI\n  /** @private */\n  protected callbacks: QuickJSModuleCallbacks\n  /** @private */\n  protected module: EitherModule\n\n  /** @private */\n  constructor(module: EitherModule, ffi: EitherFFI) {\n    this.module = module\n    this.ffi = ffi\n    this.callbacks = new QuickJSModuleCallbacks(module)\n  }\n\n  /**\n   * Create a runtime.\n   * Use the runtime to set limits on CPU and memory usage and configure module\n   * loading for one or more [[QuickJSContext]]s inside the runtime.\n   */\n  newRuntime(options: RuntimeOptions = {}): QuickJSRuntime {\n    const rt = new Lifetime(this.ffi.QTS_NewRuntime(), undefined, (rt_ptr) => {\n      this.callbacks.deleteRuntime(rt_ptr)\n      this.ffi.QTS_FreeRuntime(rt_ptr)\n    })\n\n    const runtime = new QuickJSRuntime({\n      module: this.module,\n      callbacks: this.callbacks,\n      ffi: this.ffi,\n      rt,\n    })\n\n    applyBaseRuntimeOptions(runtime, options)\n\n    if (options.moduleLoader) {\n      runtime.setModuleLoader(options.moduleLoader)\n    }\n\n    return runtime\n  }\n\n  /**\n   * A simplified API to create a new [[QuickJSRuntime]] and a\n   * [[QuickJSContext]] inside that runtime at the same time. The runtime will\n   * be disposed when the context is disposed.\n   */\n  newContext(options: ContextOptions = {}): QuickJSContext {\n    const runtime = this.newRuntime()\n    const context = runtime.newContext({\n      ...options,\n      ownedLifetimes: concat(runtime, options.ownedLifetimes),\n    })\n    runtime.context = context\n    return context\n  }\n\n  /**\n   * One-off evaluate code without needing to create a [[QuickJSRuntime]] or\n   * [[QuickJSContext]] explicitly.\n   *\n   * To protect against infinite loops, use the `shouldInterrupt` option. The\n   * [[shouldInterruptAfterDeadline]] function will create a time-based deadline.\n   *\n   * If you need more control over how the code executes, create a\n   * [[QuickJSRuntime]] (with [[newRuntime]]) or a [[QuickJSContext]] (with\n   * [[newContext]] or [[QuickJSRuntime.newContext]]), and use its\n   * [[QuickJSContext.evalCode]] method.\n   *\n   * Asynchronous callbacks may not run during the first call to `evalCode`. If\n   * you need to work with async code inside QuickJS, create a runtime and use\n   * [[QuickJSRuntime.executePendingJobs]].\n   *\n   * @returns The result is coerced to a native Javascript value using JSON\n   * serialization, so properties and values unsupported by JSON will be dropped.\n   *\n   * @throws If `code` throws during evaluation, the exception will be\n   * converted into a native Javascript value and thrown.\n   *\n   * @throws if `options.shouldInterrupt` interrupted execution, will throw a Error\n   * with name `\"InternalError\"` and  message `\"interrupted\"`.\n   */\n  evalCode(code: string, options: ModuleEvalOptions = {}): unknown {\n    return Scope.withScope((scope) => {\n      const vm = scope.manage(this.newContext())\n\n      applyModuleEvalRuntimeOptions(vm.runtime, options)\n      const result = vm.evalCode(code, \"eval.js\")\n\n      if (options.memoryLimitBytes !== undefined) {\n        // Remove memory limit so we can dump the result without exceeding it.\n        vm.runtime.setMemoryLimit(-1)\n      }\n\n      if (result.error) {\n        const error = vm.dump(scope.manage(result.error))\n        throw error\n      }\n\n      const value = vm.dump(scope.manage(result.value))\n      return value\n    })\n  }\n\n  /**\n   * Get a low-level interface to the QuickJS functions in this WebAssembly\n   * module.\n   * @experimental\n   * @unstable No warranty is provided with this API. It could change at any time.\n   * @private\n   */\n  getFFI(): EitherFFI {\n    return this.ffi\n  }\n}\n"]}
/**
 * The `node:crypto` module provides cryptographic functionality that includes a
 * set of wrappers for OpenSSL's hash, HMAC, cipher, decipher, sign, and verify
 * functions.
 *
 * ```js
 * const { createHmac } = await import('node:crypto');
 *
 * const secret = 'abcdefg';
 * const hash = createHmac('sha256', secret)
 *                .update('I love cupcakes')
 *                .digest('hex');
 * console.log(hash);
 * // Prints:
 * //   c0fa1bc00531bd78ef38c628449c5102aeabd49b5dc3a2a516ea6ea959d6658e
 * ```
 * @see [source](https://github.com/nodejs/node/blob/v22.x/lib/crypto.js)
 */
declare module "crypto" {
    import * as stream from "node:stream";
    import { PeerCertificate } from "node:tls";
    /**
     * SPKAC is a Certificate Signing Request mechanism originally implemented by
     * Netscape and was specified formally as part of HTML5's `keygen` element.
     *
     * `<keygen>` is deprecated since [HTML 5.2](https://www.w3.org/TR/html52/changes.html#features-removed) and new projects
     * should not use this element anymore.
     *
     * The `node:crypto` module provides the `Certificate` class for working with SPKAC
     * data. The most common usage is handling output generated by the HTML5 `<keygen>` element. Node.js uses [OpenSSL's SPKAC
     * implementation](https://www.openssl.org/docs/man3.0/man1/openssl-spkac.html) internally.
     * @since v0.11.8
     */
    class Certificate {
        /**
         * ```js
         * const { Certificate } = await import('node:crypto');
         * const spkac = getSpkacSomehow();
         * const challenge = Certificate.exportChallenge(spkac);
         * console.log(challenge.toString('utf8'));
         * // Prints: the challenge as a UTF8 string
         * ```
         * @since v9.0.0
         * @param encoding The `encoding` of the `spkac` string.
         * @return The challenge component of the `spkac` data structure, which includes a public key and a challenge.
         */
        static exportChallenge(spkac: BinaryLike): Buffer;
        /**
         * ```js
         * const { Certificate } = await import('node:crypto');
         * const spkac = getSpkacSomehow();
         * const publicKey = Certificate.exportPublicKey(spkac);
         * console.log(publicKey);
         * // Prints: the public key as <Buffer ...>
         * ```
         * @since v9.0.0
         * @param encoding The `encoding` of the `spkac` string.
         * @return The public key component of the `spkac` data structure, which includes a public key and a challenge.
         */
        static exportPublicKey(spkac: BinaryLike, encoding?: string): Buffer;
        /**
         * ```js
         * import { Buffer } from 'node:buffer';
         * const { Certificate } = await import('node:crypto');
         *
         * const spkac = getSpkacSomehow();
         * console.log(Certificate.verifySpkac(Buffer.from(spkac)));
         * // Prints: true or false
         * ```
         * @since v9.0.0
         * @param encoding The `encoding` of the `spkac` string.
         * @return `true` if the given `spkac` data structure is valid, `false` otherwise.
         */
        static verifySpkac(spkac: NodeJS.ArrayBufferView): boolean;
        /**
         * @deprecated
         * @param spkac
         * @returns The challenge component of the `spkac` data structure,
         * which includes a public key and a challenge.
         */
        exportChallenge(spkac: BinaryLike): Buffer;
        /**
         * @deprecated
         * @param spkac
         * @param encoding The encoding of the spkac string.
         * @returns The public key component of the `spkac` data structure,
         * which includes a public key and a challenge.
         */
        exportPublicKey(spkac: BinaryLike, encoding?: string): Buffer;
        /**
         * @deprecated
         * @param spkac
         * @returns `true` if the given `spkac` data structure is valid,
         * `false` otherwise.
         */
        verifySpkac(spkac: NodeJS.ArrayBufferView): boolean;
    }
    namespace constants {
        // https://nodejs.org/dist/latest-v22.x/docs/api/crypto.html#crypto-constants
        const OPENSSL_VERSION_NUMBER: number;
        /** Applies multiple bug workarounds within OpenSSL. See https://www.openssl.org/docs/man1.0.2/ssl/SSL_CTX_set_options.html for detail. */
        const SSL_OP_ALL: number;
        /** Instructs OpenSSL to allow a non-[EC]DHE-based key exchange mode for TLS v1.3 */
        const SSL_OP_ALLOW_NO_DHE_KEX: number;
        /** Allows legacy insecure renegotiation between OpenSSL and unpatched clients or servers. See https://www.openssl.org/docs/man1.0.2/ssl/SSL_CTX_set_options.html. */
        const SSL_OP_ALLOW_UNSAFE_LEGACY_RENEGOTIATION: number;
        /** Attempts to use the server's preferences instead of the client's when selecting a cipher. See https://www.openssl.org/docs/man1.0.2/ssl/SSL_CTX_set_options.html. */
        const SSL_OP_CIPHER_SERVER_PREFERENCE: number;
        /** Instructs OpenSSL to use Cisco's version identifier of DTLS_BAD_VER. */
        const SSL_OP_CISCO_ANYCONNECT: number;
        /** Instructs OpenSSL to turn on cookie exchange. */
        const SSL_OP_COOKIE_EXCHANGE: number;
        /** Instructs OpenSSL to add server-hello extension from an early version of the cryptopro draft. */
        const SSL_OP_CRYPTOPRO_TLSEXT_BUG: number;
        /** Instructs OpenSSL to disable a SSL 3.0/TLS 1.0 vulnerability workaround added in OpenSSL 0.9.6d. */
        const SSL_OP_DONT_INSERT_EMPTY_FRAGMENTS: number;
        /** Allows initial connection to servers that do not support RI. */
        const SSL_OP_LEGACY_SERVER_CONNECT: number;
        /** Instructs OpenSSL to disable support for SSL/TLS compression. */
        const SSL_OP_NO_COMPRESSION: number;
        /** Instructs OpenSSL to disable encrypt-then-MAC. */
        const SSL_OP_NO_ENCRYPT_THEN_MAC: number;
        const SSL_OP_NO_QUERY_MTU: number;
        /** Instructs OpenSSL to disable renegotiation. */
        const SSL_OP_NO_RENEGOTIATION: number;
        /** Instructs OpenSSL to always start a new session when performing renegotiation. */
        const SSL_OP_NO_SESSION_RESUMPTION_ON_RENEGOTIATION: number;
        /** Instructs OpenSSL to turn off SSL v2 */
        const SSL_OP_NO_SSLv2: number;
        /** Instructs OpenSSL to turn off SSL v3 */
        const SSL_OP_NO_SSLv3: number;
        /** Instructs OpenSSL to disable use of RFC4507bis tickets. */
        const SSL_OP_NO_TICKET: number;
        /** Instructs OpenSSL to turn off TLS v1 */
        const SSL_OP_NO_TLSv1: number;
        /** Instructs OpenSSL to turn off TLS v1.1 */
        const SSL_OP_NO_TLSv1_1: number;
        /** Instructs OpenSSL to turn off TLS v1.2 */
        const SSL_OP_NO_TLSv1_2: number;
        /** Instructs OpenSSL to turn off TLS v1.3 */
        const SSL_OP_NO_TLSv1_3: number;
        /** Instructs OpenSSL server to prioritize ChaCha20-Poly1305 when the client does. This option has no effect if `SSL_OP_CIPHER_SERVER_PREFERENCE` is not enabled. */
        const SSL_OP_PRIORITIZE_CHACHA: number;
        /** Instructs OpenSSL to disable version rollback attack detection. */
        const SSL_OP_TLS_ROLLBACK_BUG: number;
        const ENGINE_METHOD_RSA: number;
        const ENGINE_METHOD_DSA: number;
        const ENGINE_METHOD_DH: number;
        const ENGINE_METHOD_RAND: number;
        const ENGINE_METHOD_EC: number;
        const ENGINE_METHOD_CIPHERS: number;
        const ENGINE_METHOD_DIGESTS: number;
        const ENGINE_METHOD_PKEY_METHS: number;
        const ENGINE_METHOD_PKEY_ASN1_METHS: number;
        const ENGINE_METHOD_ALL: number;
        const ENGINE_METHOD_NONE: number;
        const DH_CHECK_P_NOT_SAFE_PRIME: number;
        const DH_CHECK_P_NOT_PRIME: number;
        const DH_UNABLE_TO_CHECK_GENERATOR: number;
        const DH_NOT_SUITABLE_GENERATOR: number;
        const RSA_PKCS1_PADDING: number;
        const RSA_SSLV23_PADDING: number;
        const RSA_NO_PADDING: number;
        const RSA_PKCS1_OAEP_PADDING: number;
        const RSA_X931_PADDING: number;
        const RSA_PKCS1_PSS_PADDING: number;
        /** Sets the salt length for RSA_PKCS1_PSS_PADDING to the digest size when signing or verifying. */
        const RSA_PSS_SALTLEN_DIGEST: number;
        /** Sets the salt length for RSA_PKCS1_PSS_PADDING to the maximum permissible value when signing data. */
        const RSA_PSS_SALTLEN_MAX_SIGN: number;
        /** Causes the salt length for RSA_PKCS1_PSS_PADDING to be determined automatically when verifying a signature. */
        const RSA_PSS_SALTLEN_AUTO: number;
        const POINT_CONVERSION_COMPRESSED: number;
        const POINT_CONVERSION_UNCOMPRESSED: number;
        const POINT_CONVERSION_HYBRID: number;
        /** Specifies the built-in default cipher list used by Node.js (colon-separated values). */
        const defaultCoreCipherList: string;
        /** Specifies the active default cipher list used by the current Node.js process  (colon-separated values). */
        const defaultCipherList: string;
    }
    interface HashOptions extends stream.TransformOptions {
        /**
         * For XOF hash functions such as `shake256`, the
         * outputLength option can be used to specify the desired output length in bytes.
         */
        outputLength?: number | undefined;
    }
    /** @deprecated since v10.0.0 */
    const fips: boolean;
    /**
     * Creates and returns a `Hash` object that can be used to generate hash digests
     * using the given `algorithm`. Optional `options` argument controls stream
     * behavior. For XOF hash functions such as `'shake256'`, the `outputLength` option
     * can be used to specify the desired output length in bytes.
     *
     * The `algorithm` is dependent on the available algorithms supported by the
     * version of OpenSSL on the platform. Examples are `'sha256'`, `'sha512'`, etc.
     * On recent releases of OpenSSL, `openssl list -digest-algorithms` will
     * display the available digest algorithms.
     *
     * Example: generating the sha256 sum of a file
     *
     * ```js
     * import {
     *   createReadStream,
     * } from 'node:fs';
     * import { argv } from 'node:process';
     * const {
     *   createHash,
     * } = await import('node:crypto');
     *
     * const filename = argv[2];
     *
     * const hash = createHash('sha256');
     *
     * const input = createReadStream(filename);
     * input.on('readable', () => {
     *   // Only one element is going to be produced by the
     *   // hash stream.
     *   const data = input.read();
     *   if (data)
     *     hash.update(data);
     *   else {
     *     console.log(`${hash.digest('hex')} ${filename}`);
     *   }
     * });
     * ```
     * @since v0.1.92
     * @param options `stream.transform` options
     */
    function createHash(algorithm: string, options?: HashOptions): Hash;
    /**
     * Creates and returns an `Hmac` object that uses the given `algorithm` and `key`.
     * Optional `options` argument controls stream behavior.
     *
     * The `algorithm` is dependent on the available algorithms supported by the
     * version of OpenSSL on the platform. Examples are `'sha256'`, `'sha512'`, etc.
     * On recent releases of OpenSSL, `openssl list -digest-algorithms` will
     * display the available digest algorithms.
     *
     * The `key` is the HMAC key used to generate the cryptographic HMAC hash. If it is
     * a `KeyObject`, its type must be `secret`. If it is a string, please consider `caveats when using strings as inputs to cryptographic APIs`. If it was
     * obtained from a cryptographically secure source of entropy, such as {@link randomBytes} or {@link generateKey}, its length should not
     * exceed the block size of `algorithm` (e.g., 512 bits for SHA-256).
     *
     * Example: generating the sha256 HMAC of a file
     *
     * ```js
     * import {
     *   createReadStream,
     * } from 'node:fs';
     * import { argv } from 'node:process';
     * const {
     *   createHmac,
     * } = await import('node:crypto');
     *
     * const filename = argv[2];
     *
     * const hmac = createHmac('sha256', 'a secret');
     *
     * const input = createReadStream(filename);
     * input.on('readable', () => {
     *   // Only one element is going to be produced by the
     *   // hash stream.
     *   const data = input.read();
     *   if (data)
     *     hmac.update(data);
     *   else {
     *     console.log(`${hmac.digest('hex')} ${filename}`);
     *   }
     * });
     * ```
     * @since v0.1.94
     * @param options `stream.transform` options
     */
    function createHmac(algorithm: string, key: BinaryLike | KeyObject, options?: stream.TransformOptions): Hmac;
    // https://nodejs.org/api/buffer.html#buffer_buffers_and_character_encodings
    type BinaryToTextEncoding = "base64" | "base64url" | "hex" | "binary";
    type CharacterEncoding = "utf8" | "utf-8" | "utf16le" | "utf-16le" | "latin1";
    type LegacyCharacterEncoding = "ascii" | "binary" | "ucs2" | "ucs-2";
    type Encoding = BinaryToTextEncoding | CharacterEncoding | LegacyCharacterEncoding;
    type ECDHKeyFormat = "compressed" | "uncompressed" | "hybrid";
    /**
     * The `Hash` class is a utility for creating hash digests of data. It can be
     * used in one of two ways:
     *
     * * As a `stream` that is both readable and writable, where data is written
     * to produce a computed hash digest on the readable side, or
     * * Using the `hash.update()` and `hash.digest()` methods to produce the
     * computed hash.
     *
     * The {@link createHash} method is used to create `Hash` instances. `Hash`objects are not to be created directly using the `new` keyword.
     *
     * Example: Using `Hash` objects as streams:
     *
     * ```js
     * const {
     *   createHash,
     * } = await import('node:crypto');
     *
     * const hash = createHash('sha256');
     *
     * hash.on('readable', () => {
     *   // Only one element is going to be produced by the
     *   // hash stream.
     *   const data = hash.read();
     *   if (data) {
     *     console.log(data.toString('hex'));
     *     // Prints:
     *     //   6a2da20943931e9834fc12cfe5bb47bbd9ae43489a30726962b576f4e3993e50
     *   }
     * });
     *
     * hash.write('some data to hash');
     * hash.end();
     * ```
     *
     * Example: Using `Hash` and piped streams:
     *
     * ```js
     * import { createReadStream } from 'node:fs';
     * import { stdout } from 'node:process';
     * const { createHash } = await import('node:crypto');
     *
     * const hash = createHash('sha256');
     *
     * const input = createReadStream('test.js');
     * input.pipe(hash).setEncoding('hex').pipe(stdout);
     * ```
     *
     * Example: Using the `hash.update()` and `hash.digest()` methods:
     *
     * ```js
     * const {
     *   createHash,
     * } = await import('node:crypto');
     *
     * const hash = createHash('sha256');
     *
     * hash.update('some data to hash');
     * console.log(hash.digest('hex'));
     * // Prints:
     * //   6a2da20943931e9834fc12cfe5bb47bbd9ae43489a30726962b576f4e3993e50
     * ```
     * @since v0.1.92
     */
    class Hash extends stream.Transform {
        private constructor();
        /**
         * Creates a new `Hash` object that contains a deep copy of the internal state
         * of the current `Hash` object.
         *
         * The optional `options` argument controls stream behavior. For XOF hash
         * functions such as `'shake256'`, the `outputLength` option can be used to
         * specify the desired output length in bytes.
         *
         * An error is thrown when an attempt is made to copy the `Hash` object after
         * its `hash.digest()` method has been called.
         *
         * ```js
         * // Calculate a rolling hash.
         * const {
         *   createHash,
         * } = await import('node:crypto');
         *
         * const hash = createHash('sha256');
         *
         * hash.update('one');
         * console.log(hash.copy().digest('hex'));
         *
         * hash.update('two');
         * console.log(hash.copy().digest('hex'));
         *
         * hash.update('three');
         * console.log(hash.copy().digest('hex'));
         *
         * // Etc.
         * ```
         * @since v13.1.0
         * @param options `stream.transform` options
         */
        copy(options?: HashOptions): Hash;
        /**
         * Updates the hash content with the given `data`, the encoding of which
         * is given in `inputEncoding`.
         * If `encoding` is not provided, and the `data` is a string, an
         * encoding of `'utf8'` is enforced. If `data` is a `Buffer`, `TypedArray`, or`DataView`, then `inputEncoding` is ignored.
         *
         * This can be called many times with new data as it is streamed.
         * @since v0.1.92
         * @param inputEncoding The `encoding` of the `data` string.
         */
        update(data: BinaryLike): Hash;
        update(data: string, inputEncoding: Encoding): Hash;
        /**
         * Calculates the digest of all of the data passed to be hashed (using the `hash.update()` method).
         * If `encoding` is provided a string will be returned; otherwise
         * a `Buffer` is returned.
         *
         * The `Hash` object can not be used again after `hash.digest()` method has been
         * called. Multiple calls will cause an error to be thrown.
         * @since v0.1.92
         * @param encoding The `encoding` of the return value.
         */
        digest(): Buffer;
        digest(encoding: BinaryToTextEncoding): string;
    }
    /**
     * The `Hmac` class is a utility for creating cryptographic HMAC digests. It can
     * be used in one of two ways:
     *
     * * As a `stream` that is both readable and writable, where data is written
     * to produce a computed HMAC digest on the readable side, or
     * * Using the `hmac.update()` and `hmac.digest()` methods to produce the
     * computed HMAC digest.
     *
     * The {@link createHmac} method is used to create `Hmac` instances. `Hmac`objects are not to be created directly using the `new` keyword.
     *
     * Example: Using `Hmac` objects as streams:
     *
     * ```js
     * const {
     *   createHmac,
     * } = await import('node:crypto');
     *
     * const hmac = createHmac('sha256', 'a secret');
     *
     * hmac.on('readable', () => {
     *   // Only one element is going to be produced by the
     *   // hash stream.
     *   const data = hmac.read();
     *   if (data) {
     *     console.log(data.toString('hex'));
     *     // Prints:
     *     //   7fd04df92f636fd450bc841c9418e5825c17f33ad9c87c518115a45971f7f77e
     *   }
     * });
     *
     * hmac.write('some data to hash');
     * hmac.end();
     * ```
     *
     * Example: Using `Hmac` and piped streams:
     *
     * ```js
     * import { createReadStream } from 'node:fs';
     * import { stdout } from 'node:process';
     * const {
     *   createHmac,
     * } = await import('node:crypto');
     *
     * const hmac = createHmac('sha256', 'a secret');
     *
     * const input = createReadStream('test.js');
     * input.pipe(hmac).pipe(stdout);
     * ```
     *
     * Example: Using the `hmac.update()` and `hmac.digest()` methods:
     *
     * ```js
     * const {
     *   createHmac,
     * } = await import('node:crypto');
     *
     * const hmac = createHmac('sha256', 'a secret');
     *
     * hmac.update('some data to hash');
     * console.log(hmac.digest('hex'));
     * // Prints:
     * //   7fd04df92f636fd450bc841c9418e5825c17f33ad9c87c518115a45971f7f77e
     * ```
     * @since v0.1.94
     * @deprecated Since v20.13.0 Calling `Hmac` class directly with `Hmac()` or `new Hmac()` is deprecated due to being internals, not intended for public use. Please use the {@link createHmac} method to create Hmac instances.
     */
    class Hmac extends stream.Transform {
        private constructor();
        /**
         * Updates the `Hmac` content with the given `data`, the encoding of which
         * is given in `inputEncoding`.
         * If `encoding` is not provided, and the `data` is a string, an
         * encoding of `'utf8'` is enforced. If `data` is a `Buffer`, `TypedArray`, or`DataView`, then `inputEncoding` is ignored.
         *
         * This can be called many times with new data as it is streamed.
         * @since v0.1.94
         * @param inputEncoding The `encoding` of the `data` string.
         */
        update(data: BinaryLike): Hmac;
        update(data: string, inputEncoding: Encoding): Hmac;
        /**
         * Calculates the HMAC digest of all of the data passed using `hmac.update()`.
         * If `encoding` is
         * provided a string is returned; otherwise a `Buffer` is returned;
         *
         * The `Hmac` object can not be used again after `hmac.digest()` has been
         * called. Multiple calls to `hmac.digest()` will result in an error being thrown.
         * @since v0.1.94
         * @param encoding The `encoding` of the return value.
         */
        digest(): Buffer;
        digest(encoding: BinaryToTextEncoding): string;
    }
    type KeyObjectType = "secret" | "public" | "private";
    interface KeyExportOptions<T extends KeyFormat> {
        type: "pkcs1" | "spki" | "pkcs8" | "sec1";
        format: T;
        cipher?: string | undefined;
        passphrase?: string | Buffer | undefined;
    }
    interface JwkKeyExportOptions {
        format: "jwk";
    }
    interface JsonWebKey {
        crv?: string | undefined;
        d?: string | undefined;
        dp?: string | undefined;
        dq?: string | undefined;
        e?: string | undefined;
        k?: string | undefined;
        kty?: string | undefined;
        n?: string | undefined;
        p?: string | undefined;
        q?: string | undefined;
        qi?: string | undefined;
        x?: string | undefined;
        y?: string | undefined;
        [key: string]: unknown;
    }
    interface AsymmetricKeyDetails {
        /**
         * Key size in bits (RSA, DSA).
         */
        modulusLength?: number | undefined;
        /**
         * Public exponent (RSA).
         */
        publicExponent?: bigint | undefined;
        /**
         * Name of the message digest (RSA-PSS).
         */
        hashAlgorithm?: string | undefined;
        /**
         * Name of the message digest used by MGF1 (RSA-PSS).
         */
        mgf1HashAlgorithm?: string | undefined;
        /**
         * Minimal salt length in bytes (RSA-PSS).
         */
        saltLength?: number | undefined;
        /**
         * Size of q in bits (DSA).
         */
        divisorLength?: number | undefined;
        /**
         * Name of the curve (EC).
         */
        namedCurve?: string | undefined;
    }
    /**
     * Node.js uses a `KeyObject` class to represent a symmetric or asymmetric key,
     * and each kind of key exposes different functions. The {@link createSecretKey}, {@link createPublicKey} and {@link createPrivateKey} methods are used to create `KeyObject`instances. `KeyObject`
     * objects are not to be created directly using the `new`keyword.
     *
     * Most applications should consider using the new `KeyObject` API instead of
     * passing keys as strings or `Buffer`s due to improved security features.
     *
     * `KeyObject` instances can be passed to other threads via `postMessage()`.
     * The receiver obtains a cloned `KeyObject`, and the `KeyObject` does not need to
     * be listed in the `transferList` argument.
     * @since v11.6.0
     */
    class KeyObject {
        private constructor();
        /**
         * Example: Converting a `CryptoKey` instance to a `KeyObject`:
         *
         * ```js
         * const { KeyObject } = await import('node:crypto');
         * const { subtle } = globalThis.crypto;
         *
         * const key = await subtle.generateKey({
         *   name: 'HMAC',
         *   hash: 'SHA-256',
         *   length: 256,
         * }, true, ['sign', 'verify']);
         *
         * const keyObject = KeyObject.from(key);
         * console.log(keyObject.symmetricKeySize);
         * // Prints: 32 (symmetric key size in bytes)
         * ```
         * @since v15.0.0
         */
        static from(key: webcrypto.CryptoKey): KeyObject;
        /**
         * For asymmetric keys, this property represents the type of the key. Supported key
         * types are:
         *
         * * `'rsa'` (OID 1.2.840.113549.1.1.1)
         * * `'rsa-pss'` (OID 1.2.840.113549.1.1.10)
         * * `'dsa'` (OID 1.2.840.10040.4.1)
         * * `'ec'` (OID 1.2.840.10045.2.1)
         * * `'x25519'` (OID 1.3.101.110)
         * * `'x448'` (OID 1.3.101.111)
         * * `'ed25519'` (OID 1.3.101.112)
         * * `'ed448'` (OID 1.3.101.113)
         * * `'dh'` (OID 1.2.840.113549.1.3.1)
         *
         * This property is `undefined` for unrecognized `KeyObject` types and symmetric
         * keys.
         * @since v11.6.0
         */
        asymmetricKeyType?: KeyType | undefined;
        /**
         * This property exists only on asymmetric keys. Depending on the type of the key,
         * this object contains information about the key. None of the information obtained
         * through this property can be used to uniquely identify a key or to compromise
         * the security of the key.
         *
         * For RSA-PSS keys, if the key material contains a `RSASSA-PSS-params` sequence,
         * the `hashAlgorithm`, `mgf1HashAlgorithm`, and `saltLength` properties will be
         * set.
         *
         * Other key details might be exposed via this API using additional attributes.
         * @since v15.7.0
         */
        asymmetricKeyDetails?: AsymmetricKeyDetails | undefined;
        /**
         * For symmetric keys, the following encoding options can be used:
         *
         * For public keys, the following encoding options can be used:
         *
         * For private keys, the following encoding options can be used:
         *
         * The result type depends on the selected encoding format, when PEM the
         * result is a string, when DER it will be a buffer containing the data
         * encoded as DER, when [JWK](https://tools.ietf.org/html/rfc7517) it will be an object.
         *
         * When [JWK](https://tools.ietf.org/html/rfc7517) encoding format was selected, all other encoding options are
         * ignored.
         *
         * PKCS#1, SEC1, and PKCS#8 type keys can be encrypted by using a combination of
         * the `cipher` and `format` options. The PKCS#8 `type` can be used with any`format` to encrypt any key algorithm (RSA, EC, or DH) by specifying a`cipher`. PKCS#1 and SEC1 can only be
         * encrypted by specifying a `cipher`when the PEM `format` is used. For maximum compatibility, use PKCS#8 for
         * encrypted private keys. Since PKCS#8 defines its own
         * encryption mechanism, PEM-level encryption is not supported when encrypting
         * a PKCS#8 key. See [RFC 5208](https://www.rfc-editor.org/rfc/rfc5208.txt) for PKCS#8 encryption and [RFC 1421](https://www.rfc-editor.org/rfc/rfc1421.txt) for
         * PKCS#1 and SEC1 encryption.
         * @since v11.6.0
         */
        export(options: KeyExportOptions<"pem">): string | Buffer;
        export(options?: KeyExportOptions<"der">): Buffer;
        export(options?: JwkKeyExportOptions): JsonWebKey;
        /**
         * Returns `true` or `false` depending on whether the keys have exactly the same
         * type, value, and parameters. This method is not [constant time](https://en.wikipedia.org/wiki/Timing_attack).
         * @since v17.7.0, v16.15.0
         * @param otherKeyObject A `KeyObject` with which to compare `keyObject`.
         */
        equals(otherKeyObject: KeyObject): boolean;
        /**
         * For secret keys, this property represents the size of the key in bytes. This
         * property is `undefined` for asymmetric keys.
         * @since v11.6.0
         */
        symmetricKeySize?: number | undefined;
        /**
         * Converts a `KeyObject` instance to a `CryptoKey`.
         * @since 22.10.0
         */
        toCryptoKey(
            algorithm:
                | webcrypto.AlgorithmIdentifier
                | webcrypto.RsaHashedImportParams
                | webcrypto.EcKeyImportParams
                | webcrypto.HmacImportParams,
            extractable: boolean,
            keyUsages: readonly webcrypto.KeyUsage[],
        ): webcrypto.CryptoKey;
        /**
         * Depending on the type of this `KeyObject`, this property is either`'secret'` for secret (symmetric) keys, `'public'` for public (asymmetric) keys
         * or `'private'` for private (asymmetric) keys.
         * @since v11.6.0
         */
        type: KeyObjectType;
    }
    type CipherCCMTypes = "aes-128-ccm" | "aes-192-ccm" | "aes-256-ccm";
    type CipherGCMTypes = "aes-128-gcm" | "aes-192-gcm" | "aes-256-gcm";
    type CipherOCBTypes = "aes-128-ocb" | "aes-192-ocb" | "aes-256-ocb";
    type CipherChaCha20Poly1305Types = "chacha20-poly1305";
    type BinaryLike = string | NodeJS.ArrayBufferView;
    type CipherKey = BinaryLike | KeyObject;
    interface CipherCCMOptions extends stream.TransformOptions {
        authTagLength: number;
    }
    interface CipherGCMOptions extends stream.TransformOptions {
        authTagLength?: number | undefined;
    }
    interface CipherOCBOptions extends stream.TransformOptions {
        authTagLength: number;
    }
    interface CipherChaCha20Poly1305Options extends stream.TransformOptions {
        /** @default 16 */
        authTagLength?: number | undefined;
    }
    /**
     * Creates and returns a `Cipher` object, with the given `algorithm`, `key` and
     * initialization vector (`iv`).
     *
     * The `options` argument controls stream behavior and is optional except when a
     * cipher in CCM or OCB mode (e.g. `'aes-128-ccm'`) is used. In that case, the`authTagLength` option is required and specifies the length of the
     * authentication tag in bytes, see `CCM mode`. In GCM mode, the `authTagLength`option is not required but can be used to set the length of the authentication
     * tag that will be returned by `getAuthTag()` and defaults to 16 bytes.
     * For `chacha20-poly1305`, the `authTagLength` option defaults to 16 bytes.
     *
     * The `algorithm` is dependent on OpenSSL, examples are `'aes192'`, etc. On
     * recent OpenSSL releases, `openssl list -cipher-algorithms` will
     * display the available cipher algorithms.
     *
     * The `key` is the raw key used by the `algorithm` and `iv` is an [initialization vector](https://en.wikipedia.org/wiki/Initialization_vector). Both arguments must be `'utf8'` encoded
     * strings,`Buffers`, `TypedArray`, or `DataView`s. The `key` may optionally be
     * a `KeyObject` of type `secret`. If the cipher does not need
     * an initialization vector, `iv` may be `null`.
     *
     * When passing strings for `key` or `iv`, please consider `caveats when using strings as inputs to cryptographic APIs`.
     *
     * Initialization vectors should be unpredictable and unique; ideally, they will be
     * cryptographically random. They do not have to be secret: IVs are typically just
     * added to ciphertext messages unencrypted. It may sound contradictory that
     * something has to be unpredictable and unique, but does not have to be secret;
     * remember that an attacker must not be able to predict ahead of time what a
     * given IV will be.
     * @since v0.1.94
     * @param options `stream.transform` options
     */
    function createCipheriv(
        algorithm: CipherCCMTypes,
        key: CipherKey,
        iv: BinaryLike,
        options: CipherCCMOptions,
    ): CipherCCM;
    function createCipheriv(
        algorithm: CipherOCBTypes,
        key: CipherKey,
        iv: BinaryLike,
        options: CipherOCBOptions,
    ): CipherOCB;
    function createCipheriv(
        algorithm: CipherGCMTypes,
        key: CipherKey,
        iv: BinaryLike,
        options?: CipherGCMOptions,
    ): CipherGCM;
    function createCipheriv(
        algorithm: CipherChaCha20Poly1305Types,
        key: CipherKey,
        iv: BinaryLike,
        options?: CipherChaCha20Poly1305Options,
    ): CipherChaCha20Poly1305;
    function createCipheriv(
        algorithm: string,
        key: CipherKey,
        iv: BinaryLike | null,
        options?: stream.TransformOptions,
    ): Cipher;
    /**
     * Instances of the `Cipher` class are used to encrypt data. The class can be
     * used in one of two ways:
     *
     * * As a `stream` that is both readable and writable, where plain unencrypted
     * data is written to produce encrypted data on the readable side, or
     * * Using the `cipher.update()` and `cipher.final()` methods to produce
     * the encrypted data.
     *
     * The {@link createCipheriv} method is
     * used to create `Cipher` instances. `Cipher` objects are not to be created
     * directly using the `new` keyword.
     *
     * Example: Using `Cipher` objects as streams:
     *
     * ```js
     * const {
     *   scrypt,
     *   randomFill,
     *   createCipheriv,
     * } = await import('node:crypto');
     *
     * const algorithm = 'aes-192-cbc';
     * const password = 'Password used to generate key';
     *
     * // First, we'll generate the key. The key length is dependent on the algorithm.
     * // In this case for aes192, it is 24 bytes (192 bits).
     * scrypt(password, 'salt', 24, (err, key) => {
     *   if (err) throw err;
     *   // Then, we'll generate a random initialization vector
     *   randomFill(new Uint8Array(16), (err, iv) => {
     *     if (err) throw err;
     *
     *     // Once we have the key and iv, we can create and use the cipher...
     *     const cipher = createCipheriv(algorithm, key, iv);
     *
     *     let encrypted = '';
     *     cipher.setEncoding('hex');
     *
     *     cipher.on('data', (chunk) => encrypted += chunk);
     *     cipher.on('end', () => console.log(encrypted));
     *
     *     cipher.write('some clear text data');
     *     cipher.end();
     *   });
     * });
     * ```
     *
     * Example: Using `Cipher` and piped streams:
     *
     * ```js
     * import {
     *   createReadStream,
     *   createWriteStream,
     * } from 'node:fs';
     *
     * import {
     *   pipeline,
     * } from 'node:stream';
     *
     * const {
     *   scrypt,
     *   randomFill,
     *   createCipheriv,
     * } = await import('node:crypto');
     *
     * const algorithm = 'aes-192-cbc';
     * const password = 'Password used to generate key';
     *
     * // First, we'll generate the key. The key length is dependent on the algorithm.
     * // In this case for aes192, it is 24 bytes (192 bits).
     * scrypt(password, 'salt', 24, (err, key) => {
     *   if (err) throw err;
     *   // Then, we'll generate a random initialization vector
     *   randomFill(new Uint8Array(16), (err, iv) => {
     *     if (err) throw err;
     *
     *     const cipher = createCipheriv(algorithm, key, iv);
     *
     *     const input = createReadStream('test.js');
     *     const output = createWriteStream('test.enc');
     *
     *     pipeline(input, cipher, output, (err) => {
     *       if (err) throw err;
     *     });
     *   });
     * });
     * ```
     *
     * Example: Using the `cipher.update()` and `cipher.final()` methods:
     *
     * ```js
     * const {
     *   scrypt,
     *   randomFill,
     *   createCipheriv,
     * } = await import('node:crypto');
     *
     * const algorithm = 'aes-192-cbc';
     * const password = 'Password used to generate key';
     *
     * // First, we'll generate the key. The key length is dependent on the algorithm.
     * // In this case for aes192, it is 24 bytes (192 bits).
     * scrypt(password, 'salt', 24, (err, key) => {
     *   if (err) throw err;
     *   // Then, we'll generate a random initialization vector
     *   randomFill(new Uint8Array(16), (err, iv) => {
     *     if (err) throw err;
     *
     *     const cipher = createCipheriv(algorithm, key, iv);
     *
     *     let encrypted = cipher.update('some clear text data', 'utf8', 'hex');
     *     encrypted += cipher.final('hex');
     *     console.log(encrypted);
     *   });
     * });
     * ```
     * @since v0.1.94
     */
    class Cipher extends stream.Transform {
        private constructor();
        /**
         * Updates the cipher with `data`. If the `inputEncoding` argument is given,
         * the `data`argument is a string using the specified encoding. If the `inputEncoding`argument is not given, `data` must be a `Buffer`, `TypedArray`, or `DataView`. If `data` is a `Buffer`,
         * `TypedArray`, or `DataView`, then `inputEncoding` is ignored.
         *
         * The `outputEncoding` specifies the output format of the enciphered
         * data. If the `outputEncoding`is specified, a string using the specified encoding is returned. If no`outputEncoding` is provided, a `Buffer` is returned.
         *
         * The `cipher.update()` method can be called multiple times with new data until `cipher.final()` is called. Calling `cipher.update()` after `cipher.final()` will result in an error being
         * thrown.
         * @since v0.1.94
         * @param inputEncoding The `encoding` of the data.
         * @param outputEncoding The `encoding` of the return value.
         */
        update(data: BinaryLike): Buffer;
        update(data: string, inputEncoding: Encoding): Buffer;
        update(data: NodeJS.ArrayBufferView, inputEncoding: undefined, outputEncoding: Encoding): string;
        update(data: string, inputEncoding: Encoding | undefined, outputEncoding: Encoding): string;
        /**
         * Once the `cipher.final()` method has been called, the `Cipher` object can no
         * longer be used to encrypt data. Attempts to call `cipher.final()` more than
         * once will result in an error being thrown.
         * @since v0.1.94
         * @param outputEncoding The `encoding` of the return value.
         * @return Any remaining enciphered contents. If `outputEncoding` is specified, a string is returned. If an `outputEncoding` is not provided, a {@link Buffer} is returned.
         */
        final(): Buffer;
        final(outputEncoding: BufferEncoding): string;
        /**
         * When using block encryption algorithms, the `Cipher` class will automatically
         * add padding to the input data to the appropriate block size. To disable the
         * default padding call `cipher.setAutoPadding(false)`.
         *
         * When `autoPadding` is `false`, the length of the entire input data must be a
         * multiple of the cipher's block size or `cipher.final()` will throw an error.
         * Disabling automatic padding is useful for non-standard padding, for instance
         * using `0x0` instead of PKCS padding.
         *
         * The `cipher.setAutoPadding()` method must be called before `cipher.final()`.
         * @since v0.7.1
         * @param [autoPadding=true]
         * @return for method chaining.
         */
        setAutoPadding(autoPadding?: boolean): this;
    }
    interface CipherCCM extends Cipher {
        setAAD(
            buffer: NodeJS.ArrayBufferView,
            options: {
                plaintextLength: number;
            },
        ): this;
        getAuthTag(): Buffer;
    }
    interface CipherGCM extends Cipher {
        setAAD(
            buffer: NodeJS.ArrayBufferView,
            options?: {
                plaintextLength: number;
            },
        ): this;
        getAuthTag(): Buffer;
    }
    interface CipherOCB extends Cipher {
        setAAD(
            buffer: NodeJS.ArrayBufferView,
            options?: {
                plaintextLength: number;
            },
        ): this;
        getAuthTag(): Buffer;
    }
    interface CipherChaCha20Poly1305 extends Cipher {
        setAAD(
            buffer: NodeJS.ArrayBufferView,
            options: {
                plaintextLength: number;
            },
        ): this;
        getAuthTag(): Buffer;
    }
    /**
     * Creates and returns a `Decipher` object that uses the given `algorithm`, `key` and initialization vector (`iv`).
     *
     * The `options` argument controls stream behavior and is optional except when a
     * cipher in CCM or OCB mode (e.g. `'aes-128-ccm'`) is used. In that case, the `authTagLength` option is required and specifies the length of the
     * authentication tag in bytes, see `CCM mode`. In GCM mode, the `authTagLength` option is not required but can be used to restrict accepted authentication tags
     * to those with the specified length.
     * For `chacha20-poly1305`, the `authTagLength` option defaults to 16 bytes.
     *
     * The `algorithm` is dependent on OpenSSL, examples are `'aes192'`, etc. On
     * recent OpenSSL releases, `openssl list -cipher-algorithms` will
     * display the available cipher algorithms.
     *
     * The `key` is the raw key used by the `algorithm` and `iv` is an [initialization vector](https://en.wikipedia.org/wiki/Initialization_vector). Both arguments must be `'utf8'` encoded
     * strings,`Buffers`, `TypedArray`, or `DataView`s. The `key` may optionally be
     * a `KeyObject` of type `secret`. If the cipher does not need
     * an initialization vector, `iv` may be `null`.
     *
     * When passing strings for `key` or `iv`, please consider `caveats when using strings as inputs to cryptographic APIs`.
     *
     * Initialization vectors should be unpredictable and unique; ideally, they will be
     * cryptographically random. They do not have to be secret: IVs are typically just
     * added to ciphertext messages unencrypted. It may sound contradictory that
     * something has to be unpredictable and unique, but does not have to be secret;
     * remember that an attacker must not be able to predict ahead of time what a given
     * IV will be.
     * @since v0.1.94
     * @param options `stream.transform` options
     */
    function createDecipheriv(
        algorithm: CipherCCMTypes,
        key: CipherKey,
        iv: BinaryLike,
        options: CipherCCMOptions,
    ): DecipherCCM;
    function createDecipheriv(
        algorithm: CipherOCBTypes,
        key: CipherKey,
        iv: BinaryLike,
        options: CipherOCBOptions,
    ): DecipherOCB;
    function createDecipheriv(
        algorithm: CipherGCMTypes,
        key: CipherKey,
        iv: BinaryLike,
        options?: CipherGCMOptions,
    ): DecipherGCM;
    function createDecipheriv(
        algorithm: CipherChaCha20Poly1305Types,
        key: CipherKey,
        iv: BinaryLike,
        options?: CipherChaCha20Poly1305Options,
    ): DecipherChaCha20Poly1305;
    function createDecipheriv(
        algorithm: string,
        key: CipherKey,
        iv: BinaryLike | null,
        options?: stream.TransformOptions,
    ): Decipher;
    /**
     * Instances of the `Decipher` class are used to decrypt data. The class can be
     * used in one of two ways:
     *
     * * As a `stream` that is both readable and writable, where plain encrypted
     * data is written to produce unencrypted data on the readable side, or
     * * Using the `decipher.update()` and `decipher.final()` methods to
     * produce the unencrypted data.
     *
     * The {@link createDecipheriv} method is
     * used to create `Decipher` instances. `Decipher` objects are not to be created
     * directly using the `new` keyword.
     *
     * Example: Using `Decipher` objects as streams:
     *
     * ```js
     * import { Buffer } from 'node:buffer';
     * const {
     *   scryptSync,
     *   createDecipheriv,
     * } = await import('node:crypto');
     *
     * const algorithm = 'aes-192-cbc';
     * const password = 'Password used to generate key';
     * // Key length is dependent on the algorithm. In this case for aes192, it is
     * // 24 bytes (192 bits).
     * // Use the async `crypto.scrypt()` instead.
     * const key = scryptSync(password, 'salt', 24);
     * // The IV is usually passed along with the ciphertext.
     * const iv = Buffer.alloc(16, 0); // Initialization vector.
     *
     * const decipher = createDecipheriv(algorithm, key, iv);
     *
     * let decrypted = '';
     * decipher.on('readable', () => {
     *   let chunk;
     *   while (null !== (chunk = decipher.read())) {
     *     decrypted += chunk.toString('utf8');
     *   }
     * });
     * decipher.on('end', () => {
     *   console.log(decrypted);
     *   // Prints: some clear text data
     * });
     *
     * // Encrypted with same algorithm, key and iv.
     * const encrypted =
     *   'e5f79c5915c02171eec6b212d5520d44480993d7d622a7c4c2da32f6efda0ffa';
     * decipher.write(encrypted, 'hex');
     * decipher.end();
     * ```
     *
     * Example: Using `Decipher` and piped streams:
     *
     * ```js
     * import {
     *   createReadStream,
     *   createWriteStream,
     * } from 'node:fs';
     * import { Buffer } from 'node:buffer';
     * const {
     *   scryptSync,
     *   createDecipheriv,
     * } = await import('node:crypto');
     *
     * const algorithm = 'aes-192-cbc';
     * const password = 'Password used to generate key';
     * // Use the async `crypto.scrypt()` instead.
     * const key = scryptSync(password, 'salt', 24);
     * // The IV is usually passed along with the ciphertext.
     * const iv = Buffer.alloc(16, 0); // Initialization vector.
     *
     * const decipher = createDecipheriv(algorithm, key, iv);
     *
     * const input = createReadStream('test.enc');
     * const output = createWriteStream('test.js');
     *
     * input.pipe(decipher).pipe(output);
     * ```
     *
     * Example: Using the `decipher.update()` and `decipher.final()` methods:
     *
     * ```js
     * import { Buffer } from 'node:buffer';
     * const {
     *   scryptSync,
     *   createDecipheriv,
     * } = await import('node:crypto');
     *
     * const algorithm = 'aes-192-cbc';
     * const password = 'Password used to generate key';
     * // Use the async `crypto.scrypt()` instead.
     * const key = scryptSync(password, 'salt', 24);
     * // The IV is usually passed along with the ciphertext.
     * const iv = Buffer.alloc(16, 0); // Initialization vector.
     *
     * const decipher = createDecipheriv(algorithm, key, iv);
     *
     * // Encrypted using same algorithm, key and iv.
     * const encrypted =
     *   'e5f79c5915c02171eec6b212d5520d44480993d7d622a7c4c2da32f6efda0ffa';
     * let decrypted = decipher.update(encrypted, 'hex', 'utf8');
     * decrypted += decipher.final('utf8');
     * console.log(decrypted);
     * // Prints: some clear text data
     * ```
     * @since v0.1.94
     */
    class Decipher extends stream.Transform {
        private constructor();
        /**
         * Updates the decipher with `data`. If the `inputEncoding` argument is given,
         * the `data` argument is a string using the specified encoding. If the `inputEncoding` argument is not given, `data` must be a `Buffer`. If `data` is a `Buffer` then `inputEncoding` is
         * ignored.
         *
         * The `outputEncoding` specifies the output format of the enciphered
         * data. If the `outputEncoding` is specified, a string using the specified encoding is returned. If no `outputEncoding` is provided, a `Buffer` is returned.
         *
         * The `decipher.update()` method can be called multiple times with new data until `decipher.final()` is called. Calling `decipher.update()` after `decipher.final()` will result in an error
         * being thrown.
         * @since v0.1.94
         * @param inputEncoding The `encoding` of the `data` string.
         * @param outputEncoding The `encoding` of the return value.
         */
        update(data: NodeJS.ArrayBufferView): Buffer;
        update(data: string, inputEncoding: Encoding): Buffer;
        update(data: NodeJS.ArrayBufferView, inputEncoding: undefined, outputEncoding: Encoding): string;
        update(data: string, inputEncoding: Encoding | undefined, outputEncoding: Encoding): string;
        /**
         * Once the `decipher.final()` method has been called, the `Decipher` object can
         * no longer be used to decrypt data. Attempts to call `decipher.final()` more
         * than once will result in an error being thrown.
         * @since v0.1.94
         * @param outputEncoding The `encoding` of the return value.
         * @return Any remaining deciphered contents. If `outputEncoding` is specified, a string is returned. If an `outputEncoding` is not provided, a {@link Buffer} is returned.
         */
        final(): Buffer;
        final(outputEncoding: BufferEncoding): string;
        /**
         * When data has been encrypted without standard block padding, calling `decipher.setAutoPadding(false)` will disable automatic padding to prevent `decipher.final()` from checking for and
         * removing padding.
         *
         * Turning auto padding off will only work if the input data's length is a
         * multiple of the ciphers block size.
         *
         * The `decipher.setAutoPadding()` method must be called before `decipher.final()`.
         * @since v0.7.1
         * @param [autoPadding=true]
         * @return for method chaining.
         */
        setAutoPadding(auto_padding?: boolean): this;
    }
    interface DecipherCCM extends Decipher {
        setAuthTag(buffer: NodeJS.ArrayBufferView): this;
        setAAD(
            buffer: NodeJS.ArrayBufferView,
            options: {
                plaintextLength: number;
            },
        ): this;
    }
    interface DecipherGCM extends Decipher {
        setAuthTag(buffer: NodeJS.ArrayBufferView): this;
        setAAD(
            buffer: NodeJS.ArrayBufferView,
            options?: {
                plaintextLength: number;
            },
        ): this;
    }
    interface DecipherOCB extends Decipher {
        setAuthTag(buffer: NodeJS.ArrayBufferView): this;
        setAAD(
            buffer: NodeJS.ArrayBufferView,
            options?: {
                plaintextLength: number;
            },
        ): this;
    }
    interface DecipherChaCha20Poly1305 extends Decipher {
        setAuthTag(buffer: NodeJS.ArrayBufferView): this;
        setAAD(
            buffer: NodeJS.ArrayBufferView,
            options: {
                plaintextLength: number;
            },
        ): this;
    }
    interface PrivateKeyInput {
        key: string | Buffer;
        format?: KeyFormat | undefined;
        type?: "pkcs1" | "pkcs8" | "sec1" | undefined;
        passphrase?: string | Buffer | undefined;
        encoding?: string | undefined;
    }
    interface PublicKeyInput {
        key: string | Buffer;
        format?: KeyFormat | undefined;
        type?: "pkcs1" | "spki" | undefined;
        encoding?: string | undefined;
    }
    /**
     * Asynchronously generates a new random secret key of the given `length`. The `type` will determine which validations will be performed on the `length`.
     *
     * ```js
     * const {
     *   generateKey,
     * } = await import('node:crypto');
     *
     * generateKey('hmac', { length: 512 }, (err, key) => {
     *   if (err) throw err;
     *   console.log(key.export().toString('hex'));  // 46e..........620
     * });
     * ```
     *
     * The size of a generated HMAC key should not exceed the block size of the
     * underlying hash function. See {@link createHmac} for more information.
     * @since v15.0.0
     * @param type The intended use of the generated secret key. Currently accepted values are `'hmac'` and `'aes'`.
     */
    function generateKey(
        type: "hmac" | "aes",
        options: {
            length: number;
        },
        callback: (err: Error | null, key: KeyObject) => void,
    ): void;
    /**
     * Synchronously generates a new random secret key of the given `length`. The `type` will determine which validations will be performed on the `length`.
     *
     * ```js
     * const {
     *   generateKeySync,
     * } = await import('node:crypto');
     *
     * const key = generateKeySync('hmac', { length: 512 });
     * console.log(key.export().toString('hex'));  // e89..........41e
     * ```
     *
     * The size of a generated HMAC key should not exceed the block size of the
     * underlying hash function. See {@link createHmac} for more information.
     * @since v15.0.0
     * @param type The intended use of the generated secret key. Currently accepted values are `'hmac'` and `'aes'`.
     */
    function generateKeySync(
        type: "hmac" | "aes",
        options: {
            length: number;
        },
    ): KeyObject;
    interface JsonWebKeyInput {
        key: JsonWebKey;
        format: "jwk";
    }
    /**
     * Creates and returns a new key object containing a private key. If `key` is a
     * string or `Buffer`, `format` is assumed to be `'pem'`; otherwise, `key` must be an object with the properties described above.
     *
     * If the private key is encrypted, a `passphrase` must be specified. The length
     * of the passphrase is limited to 1024 bytes.
     * @since v11.6.0
     */
    function createPrivateKey(key: PrivateKeyInput | string | Buffer | JsonWebKeyInput): KeyObject;
    /**
     * Creates and returns a new key object containing a public key. If `key` is a
     * string or `Buffer`, `format` is assumed to be `'pem'`; if `key` is a `KeyObject` with type `'private'`, the public key is derived from the given private key;
     * otherwise, `key` must be an object with the properties described above.
     *
     * If the format is `'pem'`, the `'key'` may also be an X.509 certificate.
     *
     * Because public keys can be derived from private keys, a private key may be
     * passed instead of a public key. In that case, this function behaves as if {@link createPrivateKey} had been called, except that the type of the
     * returned `KeyObject` will be `'public'` and that the private key cannot be
     * extracted from the returned `KeyObject`. Similarly, if a `KeyObject` with type `'private'` is given, a new `KeyObject` with type `'public'` will be returned
     * and it will be impossible to extract the private key from the returned object.
     * @since v11.6.0
     */
    function createPublicKey(key: PublicKeyInput | string | Buffer | KeyObject | JsonWebKeyInput): KeyObject;
    /**
     * Creates and returns a new key object containing a secret key for symmetric
     * encryption or `Hmac`.
     * @since v11.6.0
     * @param encoding The string encoding when `key` is a string.
     */
    function createSecretKey(key: NodeJS.ArrayBufferView): KeyObject;
    function createSecretKey(key: string, encoding: BufferEncoding): KeyObject;
    /**
     * Creates and returns a `Sign` object that uses the given `algorithm`. Use {@link getHashes} to obtain the names of the available digest algorithms.
     * Optional `options` argument controls the `stream.Writable` behavior.
     *
     * In some cases, a `Sign` instance can be created using the name of a signature
     * algorithm, such as `'RSA-SHA256'`, instead of a digest algorithm. This will use
     * the corresponding digest algorithm. This does not work for all signature
     * algorithms, such as `'ecdsa-with-SHA256'`, so it is best to always use digest
     * algorithm names.
     * @since v0.1.92
     * @param options `stream.Writable` options
     */
    function createSign(algorithm: string, options?: stream.WritableOptions): Sign;
    type DSAEncoding = "der" | "ieee-p1363";
    interface SigningOptions {
        /**
         * @see crypto.constants.RSA_PKCS1_PADDING
         */
        padding?: number | undefined;
        saltLength?: number | undefined;
        dsaEncoding?: DSAEncoding | undefined;
    }
    interface SignPrivateKeyInput extends PrivateKeyInput, SigningOptions {}
    interface SignKeyObjectInput extends SigningOptions {
        key: KeyObject;
    }
    interface SignJsonWebKeyInput extends JsonWebKeyInput, SigningOptions {}
    interface VerifyPublicKeyInput extends PublicKeyInput, SigningOptions {}
    interface VerifyKeyObjectInput extends SigningOptions {
        key: KeyObject;
    }
    interface VerifyJsonWebKeyInput extends JsonWebKeyInput, SigningOptions {}
    type KeyLike = string | Buffer | KeyObject;
    /**
     * The `Sign` class is a utility for generating signatures. It can be used in one
     * of two ways:
     *
     * * As a writable `stream`, where data to be signed is written and the `sign.sign()` method is used to generate and return the signature, or
     * * Using the `sign.update()` and `sign.sign()` methods to produce the
     * signature.
     *
     * The {@link createSign} method is used to create `Sign` instances. The
     * argument is the string name of the hash function to use. `Sign` objects are not
     * to be created directly using the `new` keyword.
     *
     * Example: Using `Sign` and `Verify` objects as streams:
     *
     * ```js
     * const {
     *   generateKeyPairSync,
     *   createSign,
     *   createVerify,
     * } = await import('node:crypto');
     *
     * const { privateKey, publicKey } = generateKeyPairSync('ec', {
     *   namedCurve: 'sect239k1',
     * });
     *
     * const sign = createSign('SHA256');
     * sign.write('some data to sign');
     * sign.end();
     * const signature = sign.sign(privateKey, 'hex');
     *
     * const verify = createVerify('SHA256');
     * verify.write('some data to sign');
     * verify.end();
     * console.log(verify.verify(publicKey, signature, 'hex'));
     * // Prints: true
     * ```
     *
     * Example: Using the `sign.update()` and `verify.update()` methods:
     *
     * ```js
     * const {
     *   generateKeyPairSync,
     *   createSign,
     *   createVerify,
     * } = await import('node:crypto');
     *
     * const { privateKey, publicKey } = generateKeyPairSync('rsa', {
     *   modulusLength: 2048,
     * });
     *
     * const sign = createSign('SHA256');
     * sign.update('some data to sign');
     * sign.end();
     * const signature = sign.sign(privateKey);
     *
     * const verify = createVerify('SHA256');
     * verify.update('some data to sign');
     * verify.end();
     * console.log(verify.verify(publicKey, signature));
     * // Prints: true
     * ```
     * @since v0.1.92
     */
    class Sign extends stream.Writable {
        private constructor();
        /**
         * Updates the `Sign` content with the given `data`, the encoding of which
         * is given in `inputEncoding`.
         * If `encoding` is not provided, and the `data` is a string, an
         * encoding of `'utf8'` is enforced. If `data` is a `Buffer`, `TypedArray`, or`DataView`, then `inputEncoding` is ignored.
         *
         * This can be called many times with new data as it is streamed.
         * @since v0.1.92
         * @param inputEncoding The `encoding` of the `data` string.
         */
        update(data: BinaryLike): this;
        update(data: string, inputEncoding: Encoding): this;
        /**
         * Calculates the signature on all the data passed through using either `sign.update()` or `sign.write()`.
         *
         * If `privateKey` is not a `KeyObject`, this function behaves as if `privateKey` had been passed to {@link createPrivateKey}. If it is an
         * object, the following additional properties can be passed:
         *
         * If `outputEncoding` is provided a string is returned; otherwise a `Buffer` is returned.
         *
         * The `Sign` object can not be again used after `sign.sign()` method has been
         * called. Multiple calls to `sign.sign()` will result in an error being thrown.
         * @since v0.1.92
         */
        sign(privateKey: KeyLike | SignKeyObjectInput | SignPrivateKeyInput | SignJsonWebKeyInput): Buffer;
        sign(
            privateKey: KeyLike | SignKeyObjectInput | SignPrivateKeyInput | SignJsonWebKeyInput,
            outputFormat: BinaryToTextEncoding,
        ): string;
    }
    /**
     * Creates and returns a `Verify` object that uses the given algorithm.
     * Use {@link getHashes} to obtain an array of names of the available
     * signing algorithms. Optional `options` argument controls the `stream.Writable` behavior.
     *
     * In some cases, a `Verify` instance can be created using the name of a signature
     * algorithm, such as `'RSA-SHA256'`, instead of a digest algorithm. This will use
     * the corresponding digest algorithm. This does not work for all signature
     * algorithms, such as `'ecdsa-with-SHA256'`, so it is best to always use digest
     * algorithm names.
     * @since v0.1.92
     * @param options `stream.Writable` options
     */
    function createVerify(algorithm: string, options?: stream.WritableOptions): Verify;
    /**
     * The `Verify` class is a utility for verifying signatures. It can be used in one
     * of two ways:
     *
     * * As a writable `stream` where written data is used to validate against the
     * supplied signature, or
     * * Using the `verify.update()` and `verify.verify()` methods to verify
     * the signature.
     *
     * The {@link createVerify} method is used to create `Verify` instances. `Verify` objects are not to be created directly using the `new` keyword.
     *
     * See `Sign` for examples.
     * @since v0.1.92
     */
    class Verify extends stream.Writable {
        private constructor();
        /**
         * Updates the `Verify` content with the given `data`, the encoding of which
         * is given in `inputEncoding`.
         * If `inputEncoding` is not provided, and the `data` is a string, an
         * encoding of `'utf8'` is enforced. If `data` is a `Buffer`, `TypedArray`, or `DataView`, then `inputEncoding` is ignored.
         *
         * This can be called many times with new data as it is streamed.
         * @since v0.1.92
         * @param inputEncoding The `encoding` of the `data` string.
         */
        update(data: BinaryLike): Verify;
        update(data: string, inputEncoding: Encoding): Verify;
        /**
         * Verifies the provided data using the given `object` and `signature`.
         *
         * If `object` is not a `KeyObject`, this function behaves as if `object` had been passed to {@link createPublicKey}. If it is an
         * object, the following additional properties can be passed:
         *
         * The `signature` argument is the previously calculated signature for the data, in
         * the `signatureEncoding`.
         * If a `signatureEncoding` is specified, the `signature` is expected to be a
         * string; otherwise `signature` is expected to be a `Buffer`, `TypedArray`, or `DataView`.
         *
         * The `verify` object can not be used again after `verify.verify()` has been
         * called. Multiple calls to `verify.verify()` will result in an error being
         * thrown.
         *
         * Because public keys can be derived from private keys, a private key may
         * be passed instead of a public key.
         * @since v0.1.92
         */
        verify(
            object: KeyLike | VerifyKeyObjectInput | VerifyPublicKeyInput | VerifyJsonWebKeyInput,
            signature: NodeJS.ArrayBufferView,
        ): boolean;
        verify(
            object: KeyLike | VerifyKeyObjectInput | VerifyPublicKeyInput | VerifyJsonWebKeyInput,
            signature: string,
            signature_format?: BinaryToTextEncoding,
        ): boolean;
    }
    /**
     * Creates a `DiffieHellman` key exchange object using the supplied `prime` and an
     * optional specific `generator`.
     *
     * The `generator` argument can be a number, string, or `Buffer`. If `generator` is not specified, the value `2` is used.
     *
     * If `primeEncoding` is specified, `prime` is expected to be a string; otherwise
     * a `Buffer`, `TypedArray`, or `DataView` is expected.
     *
     * If `generatorEncoding` is specified, `generator` is expected to be a string;
     * otherwise a number, `Buffer`, `TypedArray`, or `DataView` is expected.
     * @since v0.11.12
     * @param primeEncoding The `encoding` of the `prime` string.
     * @param [generator=2]
     * @param generatorEncoding The `encoding` of the `generator` string.
     */
    function createDiffieHellman(primeLength: number, generator?: number): DiffieHellman;
    function createDiffieHellman(
        prime: ArrayBuffer | NodeJS.ArrayBufferView,
        generator?: number | ArrayBuffer | NodeJS.ArrayBufferView,
    ): DiffieHellman;
    function createDiffieHellman(
        prime: ArrayBuffer | NodeJS.ArrayBufferView,
        generator: string,
        generatorEncoding: BinaryToTextEncoding,
    ): DiffieHellman;
    function createDiffieHellman(
        prime: string,
        primeEncoding: BinaryToTextEncoding,
        generator?: number | ArrayBuffer | NodeJS.ArrayBufferView,
    ): DiffieHellman;
    function createDiffieHellman(
        prime: string,
        primeEncoding: BinaryToTextEncoding,
        generator: string,
        generatorEncoding: BinaryToTextEncoding,
    ): DiffieHellman;
    /**
     * The `DiffieHellman` class is a utility for creating Diffie-Hellman key
     * exchanges.
     *
     * Instances of the `DiffieHellman` class can be created using the {@link createDiffieHellman} function.
     *
     * ```js
     * import assert from 'node:assert';
     *
     * const {
     *   createDiffieHellman,
     * } = await import('node:crypto');
     *
     * // Generate Alice's keys...
     * const alice = createDiffieHellman(2048);
     * const aliceKey = alice.generateKeys();
     *
     * // Generate Bob's keys...
     * const bob = createDiffieHellman(alice.getPrime(), alice.getGenerator());
     * const bobKey = bob.generateKeys();
     *
     * // Exchange and generate the secret...
     * const aliceSecret = alice.computeSecret(bobKey);
     * const bobSecret = bob.computeSecret(aliceKey);
     *
     * // OK
     * assert.strictEqual(aliceSecret.toString('hex'), bobSecret.toString('hex'));
     * ```
     * @since v0.5.0
     */
    class DiffieHellman {
        private constructor();
        /**
         * Generates private and public Diffie-Hellman key values unless they have been
         * generated or computed already, and returns
         * the public key in the specified `encoding`. This key should be
         * transferred to the other party.
         * If `encoding` is provided a string is returned; otherwise a `Buffer` is returned.
         *
         * This function is a thin wrapper around [`DH_generate_key()`](https://www.openssl.org/docs/man3.0/man3/DH_generate_key.html). In particular,
         * once a private key has been generated or set, calling this function only updates
         * the public key but does not generate a new private key.
         * @since v0.5.0
         * @param encoding The `encoding` of the return value.
         */
        generateKeys(): Buffer;
        generateKeys(encoding: BinaryToTextEncoding): string;
        /**
         * Computes the shared secret using `otherPublicKey` as the other
         * party's public key and returns the computed shared secret. The supplied
         * key is interpreted using the specified `inputEncoding`, and secret is
         * encoded using specified `outputEncoding`.
         * If the `inputEncoding` is not
         * provided, `otherPublicKey` is expected to be a `Buffer`, `TypedArray`, or `DataView`.
         *
         * If `outputEncoding` is given a string is returned; otherwise, a `Buffer` is returned.
         * @since v0.5.0
         * @param inputEncoding The `encoding` of an `otherPublicKey` string.
         * @param outputEncoding The `encoding` of the return value.
         */
        computeSecret(otherPublicKey: NodeJS.ArrayBufferView, inputEncoding?: null, outputEncoding?: null): Buffer;
        computeSecret(otherPublicKey: string, inputEncoding: BinaryToTextEncoding, outputEncoding?: null): Buffer;
        computeSecret(
            otherPublicKey: NodeJS.ArrayBufferView,
            inputEncoding: null,
            outputEncoding: BinaryToTextEncoding,
        ): string;
        computeSecret(
            otherPublicKey: string,
            inputEncoding: BinaryToTextEncoding,
            outputEncoding: BinaryToTextEncoding,
        ): string;
        /**
         * Returns the Diffie-Hellman prime in the specified `encoding`.
         * If `encoding` is provided a string is
         * returned; otherwise a `Buffer` is returned.
         * @since v0.5.0
         * @param encoding The `encoding` of the return value.
         */
        getPrime(): Buffer;
        getPrime(encoding: BinaryToTextEncoding): string;
        /**
         * Returns the Diffie-Hellman generator in the specified `encoding`.
         * If `encoding` is provided a string is
         * returned; otherwise a `Buffer` is returned.
         * @since v0.5.0
         * @param encoding The `encoding` of the return value.
         */
        getGenerator(): Buffer;
        getGenerator(encoding: BinaryToTextEncoding): string;
        /**
         * Returns the Diffie-Hellman public key in the specified `encoding`.
         * If `encoding` is provided a
         * string is returned; otherwise a `Buffer` is returned.
         * @since v0.5.0
         * @param encoding The `encoding` of the return value.
         */
        getPublicKey(): Buffer;
        getPublicKey(encoding: BinaryToTextEncoding): string;
        /**
         * Returns the Diffie-Hellman private key in the specified `encoding`.
         * If `encoding` is provided a
         * string is returned; otherwise a `Buffer` is returned.
         * @since v0.5.0
         * @param encoding The `encoding` of the return value.
         */
        getPrivateKey(): Buffer;
        getPrivateKey(encoding: BinaryToTextEncoding): string;
        /**
         * Sets the Diffie-Hellman public key. If the `encoding` argument is provided, `publicKey` is expected
         * to be a string. If no `encoding` is provided, `publicKey` is expected
         * to be a `Buffer`, `TypedArray`, or `DataView`.
         * @since v0.5.0
         * @param encoding The `encoding` of the `publicKey` string.
         */
        setPublicKey(publicKey: NodeJS.ArrayBufferView): void;
        setPublicKey(publicKey: string, encoding: BufferEncoding): void;
        /**
         * Sets the Diffie-Hellman private key. If the `encoding` argument is provided,`privateKey` is expected
         * to be a string. If no `encoding` is provided, `privateKey` is expected
         * to be a `Buffer`, `TypedArray`, or `DataView`.
         *
         * This function does not automatically compute the associated public key. Either `diffieHellman.setPublicKey()` or `diffieHellman.generateKeys()` can be
         * used to manually provide the public key or to automatically derive it.
         * @since v0.5.0
         * @param encoding The `encoding` of the `privateKey` string.
         */
        setPrivateKey(privateKey: NodeJS.ArrayBufferView): void;
        setPrivateKey(privateKey: string, encoding: BufferEncoding): void;
        /**
         * A bit field containing any warnings and/or errors resulting from a check
         * performed during initialization of the `DiffieHellman` object.
         *
         * The following values are valid for this property (as defined in `node:constants` module):
         *
         * * `DH_CHECK_P_NOT_SAFE_PRIME`
         * * `DH_CHECK_P_NOT_PRIME`
         * * `DH_UNABLE_TO_CHECK_GENERATOR`
         * * `DH_NOT_SUITABLE_GENERATOR`
         * @since v0.11.12
         */
        verifyError: number;
    }
    /**
     * The `DiffieHellmanGroup` class takes a well-known modp group as its argument.
     * It works the same as `DiffieHellman`, except that it does not allow changing its keys after creation.
     * In other words, it does not implement `setPublicKey()` or `setPrivateKey()` methods.
     *
     * ```js
     * const { createDiffieHellmanGroup } = await import('node:crypto');
     * const dh = createDiffieHellmanGroup('modp1');
     * ```
     * The name (e.g. `'modp1'`) is taken from [RFC 2412](https://www.rfc-editor.org/rfc/rfc2412.txt) (modp1 and 2) and [RFC 3526](https://www.rfc-editor.org/rfc/rfc3526.txt):
     * ```bash
     * $ perl -ne 'print "$1\n" if /"(modp\d+)"/' src/node_crypto_groups.h
     * modp1  #  768 bits
     * modp2  # 1024 bits
     * modp5  # 1536 bits
     * modp14 # 2048 bits
     * modp15 # etc.
     * modp16
     * modp17
     * modp18
     * ```
     * @since v0.7.5
     */
    const DiffieHellmanGroup: DiffieHellmanGroupConstructor;
    interface DiffieHellmanGroupConstructor {
        new(name: string): DiffieHellmanGroup;
        (name: string): DiffieHellmanGroup;
        readonly prototype: DiffieHellmanGroup;
    }
    type DiffieHellmanGroup = Omit<DiffieHellman, "setPublicKey" | "setPrivateKey">;
    /**
     * Creates a predefined `DiffieHellmanGroup` key exchange object. The
     * supported groups are listed in the documentation for `DiffieHellmanGroup`.
     *
     * The returned object mimics the interface of objects created by {@link createDiffieHellman}, but will not allow changing
     * the keys (with `diffieHellman.setPublicKey()`, for example). The
     * advantage of using this method is that the parties do not have to
     * generate nor exchange a group modulus beforehand, saving both processor
     * and communication time.
     *
     * Example (obtaining a shared secret):
     *
     * ```js
     * const {
     *   getDiffieHellman,
     * } = await import('node:crypto');
     * const alice = getDiffieHellman('modp14');
     * const bob = getDiffieHellman('modp14');
     *
     * alice.generateKeys();
     * bob.generateKeys();
     *
     * const aliceSecret = alice.computeSecret(bob.getPublicKey(), null, 'hex');
     * const bobSecret = bob.computeSecret(alice.getPublicKey(), null, 'hex');
     *
     * // aliceSecret and bobSecret should be the same
     * console.log(aliceSecret === bobSecret);
     * ```
     * @since v0.7.5
     */
    function getDiffieHellman(groupName: string): DiffieHellmanGroup;
    /**
     * An alias for {@link getDiffieHellman}
     * @since v0.9.3
     */
    function createDiffieHellmanGroup(name: string): DiffieHellmanGroup;
    /**
     * Provides an asynchronous Password-Based Key Derivation Function 2 (PBKDF2)
     * implementation. A selected HMAC digest algorithm specified by `digest` is
     * applied to derive a key of the requested byte length (`keylen`) from the `password`, `salt` and `iterations`.
     *
     * The supplied `callback` function is called with two arguments: `err` and `derivedKey`. If an error occurs while deriving the key, `err` will be set;
     * otherwise `err` will be `null`. By default, the successfully generated `derivedKey` will be passed to the callback as a `Buffer`. An error will be
     * thrown if any of the input arguments specify invalid values or types.
     *
     * The `iterations` argument must be a number set as high as possible. The
     * higher the number of iterations, the more secure the derived key will be,
     * but will take a longer amount of time to complete.
     *
     * The `salt` should be as unique as possible. It is recommended that a salt is
     * random and at least 16 bytes long. See [NIST SP 800-132](https://nvlpubs.nist.gov/nistpubs/Legacy/SP/nistspecialpublication800-132.pdf) for details.
     *
     * When passing strings for `password` or `salt`, please consider `caveats when using strings as inputs to cryptographic APIs`.
     *
     * ```js
     * const {
     *   pbkdf2,
     * } = await import('node:crypto');
     *
     * pbkdf2('secret', 'salt', 100000, 64, 'sha512', (err, derivedKey) => {
     *   if (err) throw err;
     *   console.log(derivedKey.toString('hex'));  // '3745e48...08d59ae'
     * });
     * ```
     *
     * An array of supported digest functions can be retrieved using {@link getHashes}.
     *
     * This API uses libuv's threadpool, which can have surprising and
     * negative performance implications for some applications; see the `UV_THREADPOOL_SIZE` documentation for more information.
     * @since v0.5.5
     */
    function pbkdf2(
        password: BinaryLike,
        salt: BinaryLike,
        iterations: number,
        keylen: number,
        digest: string,
        callback: (err: Error | null, derivedKey: Buffer) => void,
    ): void;
    /**
     * Provides a synchronous Password-Based Key Derivation Function 2 (PBKDF2)
     * implementation. A selected HMAC digest algorithm specified by `digest` is
     * applied to derive a key of the requested byte length (`keylen`) from the `password`, `salt` and `iterations`.
     *
     * If an error occurs an `Error` will be thrown, otherwise the derived key will be
     * returned as a `Buffer`.
     *
     * The `iterations` argument must be a number set as high as possible. The
     * higher the number of iterations, the more secure the derived key will be,
     * but will take a longer amount of time to complete.
     *
     * The `salt` should be as unique as possible. It is recommended that a salt is
     * random and at least 16 bytes long. See [NIST SP 800-132](https://nvlpubs.nist.gov/nistpubs/Legacy/SP/nistspecialpublication800-132.pdf) for details.
     *
     * When passing strings for `password` or `salt`, please consider `caveats when using strings as inputs to cryptographic APIs`.
     *
     * ```js
     * const {
     *   pbkdf2Sync,
     * } = await import('node:crypto');
     *
     * const key = pbkdf2Sync('secret', 'salt', 100000, 64, 'sha512');
     * console.log(key.toString('hex'));  // '3745e48...08d59ae'
     * ```
     *
     * An array of supported digest functions can be retrieved using {@link getHashes}.
     * @since v0.9.3
     */
    function pbkdf2Sync(
        password: BinaryLike,
        salt: BinaryLike,
        iterations: number,
        keylen: number,
        digest: string,
    ): Buffer;
    /**
     * Generates cryptographically strong pseudorandom data. The `size` argument
     * is a number indicating the number of bytes to generate.
     *
     * If a `callback` function is provided, the bytes are generated asynchronously
     * and the `callback` function is invoked with two arguments: `err` and `buf`.
     * If an error occurs, `err` will be an `Error` object; otherwise it is `null`. The `buf` argument is a `Buffer` containing the generated bytes.
     *
     * ```js
     * // Asynchronous
     * const {
     *   randomBytes,
     * } = await import('node:crypto');
     *
     * randomBytes(256, (err, buf) => {
     *   if (err) throw err;
     *   console.log(`${buf.length} bytes of random data: ${buf.toString('hex')}`);
     * });
     * ```
     *
     * If the `callback` function is not provided, the random bytes are generated
     * synchronously and returned as a `Buffer`. An error will be thrown if
     * there is a problem generating the bytes.
     *
     * ```js
     * // Synchronous
     * const {
     *   randomBytes,
     * } = await import('node:crypto');
     *
     * const buf = randomBytes(256);
     * console.log(
     *   `${buf.length} bytes of random data: ${buf.toString('hex')}`);
     * ```
     *
     * The `crypto.randomBytes()` method will not complete until there is
     * sufficient entropy available.
     * This should normally never take longer than a few milliseconds. The only time
     * when generating the random bytes may conceivably block for a longer period of
     * time is right after boot, when the whole system is still low on entropy.
     *
     * This API uses libuv's threadpool, which can have surprising and
     * negative performance implications for some applications; see the `UV_THREADPOOL_SIZE` documentation for more information.
     *
     * The asynchronous version of `crypto.randomBytes()` is carried out in a single
     * threadpool request. To minimize threadpool task length variation, partition
     * large `randomBytes` requests when doing so as part of fulfilling a client
     * request.
     * @since v0.5.8
     * @param size The number of bytes to generate. The `size` must not be larger than `2**31 - 1`.
     * @return if the `callback` function is not provided.
     */
    function randomBytes(size: number): Buffer;
    function randomBytes(size: number, callback: (err: Error | null, buf: Buffer) => void): void;
    function pseudoRandomBytes(size: number): Buffer;
    function pseudoRandomBytes(size: number, callback: (err: Error | null, buf: Buffer) => void): void;
    /**
     * Return a random integer `n` such that `min <= n < max`.  This
     * implementation avoids [modulo bias](https://en.wikipedia.org/wiki/Fisher%E2%80%93Yates_shuffle#Modulo_bias).
     *
     * The range (`max - min`) must be less than 2**48. `min` and `max` must
     * be [safe integers](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isSafeInteger).
     *
     * If the `callback` function is not provided, the random integer is
     * generated synchronously.
     *
     * ```js
     * // Asynchronous
     * const {
     *   randomInt,
     * } = await import('node:crypto');
     *
     * randomInt(3, (err, n) => {
     *   if (err) throw err;
     *   console.log(`Random number chosen from (0, 1, 2): ${n}`);
     * });
     * ```
     *
     * ```js
     * // Synchronous
     * const {
     *   randomInt,
     * } = await import('node:crypto');
     *
     * const n = randomInt(3);
     * console.log(`Random number chosen from (0, 1, 2): ${n}`);
     * ```
     *
     * ```js
     * // With `min` argument
     * const {
     *   randomInt,
     * } = await import('node:crypto');
     *
     * const n = randomInt(1, 7);
     * console.log(`The dice rolled: ${n}`);
     * ```
     * @since v14.10.0, v12.19.0
     * @param [min=0] Start of random range (inclusive).
     * @param max End of random range (exclusive).
     * @param callback `function(err, n) {}`.
     */
    function randomInt(max: number): number;
    function randomInt(min: number, max: number): number;
    function randomInt(max: number, callback: (err: Error | null, value: number) => void): void;
    function randomInt(min: number, max: number, callback: (err: Error | null, value: number) => void): void;
    /**
     * Synchronous version of {@link randomFill}.
     *
     * ```js
     * import { Buffer } from 'node:buffer';
     * const { randomFillSync } = await import('node:crypto');
     *
     * const buf = Buffer.alloc(10);
     * console.log(randomFillSync(buf).toString('hex'));
     *
     * randomFillSync(buf, 5);
     * console.log(buf.toString('hex'));
     *
     * // The above is equivalent to the following:
     * randomFillSync(buf, 5, 5);
     * console.log(buf.toString('hex'));
     * ```
     *
     * Any `ArrayBuffer`, `TypedArray` or `DataView` instance may be passed as`buffer`.
     *
     * ```js
     * import { Buffer } from 'node:buffer';
     * const { randomFillSync } = await import('node:crypto');
     *
     * const a = new Uint32Array(10);
     * console.log(Buffer.from(randomFillSync(a).buffer,
     *                         a.byteOffset, a.byteLength).toString('hex'));
     *
     * const b = new DataView(new ArrayBuffer(10));
     * console.log(Buffer.from(randomFillSync(b).buffer,
     *                         b.byteOffset, b.byteLength).toString('hex'));
     *
     * const c = new ArrayBuffer(10);
     * console.log(Buffer.from(randomFillSync(c)).toString('hex'));
     * ```
     * @since v7.10.0, v6.13.0
     * @param buffer Must be supplied. The size of the provided `buffer` must not be larger than `2**31 - 1`.
     * @param [offset=0]
     * @param [size=buffer.length - offset]
     * @return The object passed as `buffer` argument.
     */
    function randomFillSync<T extends NodeJS.ArrayBufferView>(buffer: T, offset?: number, size?: number): T;
    /**
     * This function is similar to {@link randomBytes} but requires the first
     * argument to be a `Buffer` that will be filled. It also
     * requires that a callback is passed in.
     *
     * If the `callback` function is not provided, an error will be thrown.
     *
     * ```js
     * import { Buffer } from 'node:buffer';
     * const { randomFill } = await import('node:crypto');
     *
     * const buf = Buffer.alloc(10);
     * randomFill(buf, (err, buf) => {
     *   if (err) throw err;
     *   console.log(buf.toString('hex'));
     * });
     *
     * randomFill(buf, 5, (err, buf) => {
     *   if (err) throw err;
     *   console.log(buf.toString('hex'));
     * });
     *
     * // The above is equivalent to the following:
     * randomFill(buf, 5, 5, (err, buf) => {
     *   if (err) throw err;
     *   console.log(buf.toString('hex'));
     * });
     * ```
     *
     * Any `ArrayBuffer`, `TypedArray`, or `DataView` instance may be passed as `buffer`.
     *
     * While this includes instances of `Float32Array` and `Float64Array`, this
     * function should not be used to generate random floating-point numbers. The
     * result may contain `+Infinity`, `-Infinity`, and `NaN`, and even if the array
     * contains finite numbers only, they are not drawn from a uniform random
     * distribution and have no meaningful lower or upper bounds.
     *
     * ```js
     * import { Buffer } from 'node:buffer';
     * const { randomFill } = await import('node:crypto');
     *
     * const a = new Uint32Array(10);
     * randomFill(a, (err, buf) => {
     *   if (err) throw err;
     *   console.log(Buffer.from(buf.buffer, buf.byteOffset, buf.byteLength)
     *     .toString('hex'));
     * });
     *
     * const b = new DataView(new ArrayBuffer(10));
     * randomFill(b, (err, buf) => {
     *   if (err) throw err;
     *   console.log(Buffer.from(buf.buffer, buf.byteOffset, buf.byteLength)
     *     .toString('hex'));
     * });
     *
     * const c = new ArrayBuffer(10);
     * randomFill(c, (err, buf) => {
     *   if (err) throw err;
     *   console.log(Buffer.from(buf).toString('hex'));
     * });
     * ```
     *
     * This API uses libuv's threadpool, which can have surprising and
     * negative performance implications for some applications; see the `UV_THREADPOOL_SIZE` documentation for more information.
     *
     * The asynchronous version of `crypto.randomFill()` is carried out in a single
     * threadpool request. To minimize threadpool task length variation, partition
     * large `randomFill` requests when doing so as part of fulfilling a client
     * request.
     * @since v7.10.0, v6.13.0
     * @param buffer Must be supplied. The size of the provided `buffer` must not be larger than `2**31 - 1`.
     * @param [offset=0]
     * @param [size=buffer.length - offset]
     * @param callback `function(err, buf) {}`.
     */
    function randomFill<T extends NodeJS.ArrayBufferView>(
        buffer: T,
        callback: (err: Error | null, buf: T) => void,
    ): void;
    function randomFill<T extends NodeJS.ArrayBufferView>(
        buffer: T,
        offset: number,
        callback: (err: Error | null, buf: T) => void,
    ): void;
    function randomFill<T extends NodeJS.ArrayBufferView>(
        buffer: T,
        offset: number,
        size: number,
        callback: (err: Error | null, buf: T) => void,
    ): void;
    interface ScryptOptions {
        cost?: number | undefined;
        blockSize?: number | undefined;
        parallelization?: number | undefined;
        N?: number | undefined;
        r?: number | undefined;
        p?: number | undefined;
        maxmem?: number | undefined;
    }
    /**
     * Provides an asynchronous [scrypt](https://en.wikipedia.org/wiki/Scrypt) implementation. Scrypt is a password-based
     * key derivation function that is designed to be expensive computationally and
     * memory-wise in order to make brute-force attacks unrewarding.
     *
     * The `salt` should be as unique as possible. It is recommended that a salt is
     * random and at least 16 bytes long. See [NIST SP 800-132](https://nvlpubs.nist.gov/nistpubs/Legacy/SP/nistspecialpublication800-132.pdf) for details.
     *
     * When passing strings for `password` or `salt`, please consider `caveats when using strings as inputs to cryptographic APIs`.
     *
     * The `callback` function is called with two arguments: `err` and `derivedKey`. `err` is an exception object when key derivation fails, otherwise `err` is `null`. `derivedKey` is passed to the
     * callback as a `Buffer`.
     *
     * An exception is thrown when any of the input arguments specify invalid values
     * or types.
     *
     * ```js
     * const {
     *   scrypt,
     * } = await import('node:crypto');
     *
     * // Using the factory defaults.
     * scrypt('password', 'salt', 64, (err, derivedKey) => {
     *   if (err) throw err;
     *   console.log(derivedKey.toString('hex'));  // '3745e48...08d59ae'
     * });
     * // Using a custom N parameter. Must be a power of two.
     * scrypt('password', 'salt', 64, { N: 1024 }, (err, derivedKey) => {
     *   if (err) throw err;
     *   console.log(derivedKey.toString('hex'));  // '3745e48...aa39b34'
     * });
     * ```
     * @since v10.5.0
     */
    function scrypt(
        password: BinaryLike,
        salt: BinaryLike,
        keylen: number,
        callback: (err: Error | null, derivedKey: Buffer) => void,
    ): void;
    function scrypt(
        password: BinaryLike,
        salt: BinaryLike,
        keylen: number,
        options: ScryptOptions,
        callback: (err: Error | null, derivedKey: Buffer) => void,
    ): void;
    /**
     * Provides a synchronous [scrypt](https://en.wikipedia.org/wiki/Scrypt) implementation. Scrypt is a password-based
     * key derivation function that is designed to be expensive computationally and
     * memory-wise in order to make brute-force attacks unrewarding.
     *
     * The `salt` should be as unique as possible. It is recommended that a salt is
     * random and at least 16 bytes long. See [NIST SP 800-132](https://nvlpubs.nist.gov/nistpubs/Legacy/SP/nistspecialpublication800-132.pdf) for details.
     *
     * When passing strings for `password` or `salt`, please consider `caveats when using strings as inputs to cryptographic APIs`.
     *
     * An exception is thrown when key derivation fails, otherwise the derived key is
     * returned as a `Buffer`.
     *
     * An exception is thrown when any of the input arguments specify invalid values
     * or types.
     *
     * ```js
     * const {
     *   scryptSync,
     * } = await import('node:crypto');
     * // Using the factory defaults.
     *
     * const key1 = scryptSync('password', 'salt', 64);
     * console.log(key1.toString('hex'));  // '3745e48...08d59ae'
     * // Using a custom N parameter. Must be a power of two.
     * const key2 = scryptSync('password', 'salt', 64, { N: 1024 });
     * console.log(key2.toString('hex'));  // '3745e48...aa39b34'
     * ```
     * @since v10.5.0
     */
    function scryptSync(password: BinaryLike, salt: BinaryLike, keylen: number, options?: ScryptOptions): Buffer;
    interface RsaPublicKey {
        key: KeyLike;
        padding?: number | undefined;
    }
    interface RsaPrivateKey {
        key: KeyLike;
        passphrase?: string | undefined;
        /**
         * @default 'sha1'
         */
        oaepHash?: string | undefined;
        oaepLabel?: NodeJS.TypedArray | undefined;
        padding?: number | undefined;
    }
    /**
     * Encrypts the content of `buffer` with `key` and returns a new `Buffer` with encrypted content. The returned data can be decrypted using
     * the corresponding private key, for example using {@link privateDecrypt}.
     *
     * If `key` is not a `KeyObject`, this function behaves as if `key` had been passed to {@link createPublicKey}. If it is an
     * object, the `padding` property can be passed. Otherwise, this function uses `RSA_PKCS1_OAEP_PADDING`.
     *
     * Because RSA public keys can be derived from private keys, a private key may
     * be passed instead of a public key.
     * @since v0.11.14
     */
    function publicEncrypt(
        key: RsaPublicKey | RsaPrivateKey | KeyLike,
        buffer: NodeJS.ArrayBufferView | string,
    ): Buffer;
    /**
     * Decrypts `buffer` with `key`.`buffer` was previously encrypted using
     * the corresponding private key, for example using {@link privateEncrypt}.
     *
     * If `key` is not a `KeyObject`, this function behaves as if `key` had been passed to {@link createPublicKey}. If it is an
     * object, the `padding` property can be passed. Otherwise, this function uses `RSA_PKCS1_PADDING`.
     *
     * Because RSA public keys can be derived from private keys, a private key may
     * be passed instead of a public key.
     * @since v1.1.0
     */
    function publicDecrypt(
        key: RsaPublicKey | RsaPrivateKey | KeyLike,
        buffer: NodeJS.ArrayBufferView | string,
    ): Buffer;
    /**
     * Decrypts `buffer` with `privateKey`. `buffer` was previously encrypted using
     * the corresponding public key, for example using {@link publicEncrypt}.
     *
     * If `privateKey` is not a `KeyObject`, this function behaves as if `privateKey` had been passed to {@link createPrivateKey}. If it is an
     * object, the `padding` property can be passed. Otherwise, this function uses `RSA_PKCS1_OAEP_PADDING`.
     * @since v0.11.14
     */
    function privateDecrypt(privateKey: RsaPrivateKey | KeyLike, buffer: NodeJS.ArrayBufferView | string): Buffer;
    /**
     * Encrypts `buffer` with `privateKey`. The returned data can be decrypted using
     * the corresponding public key, for example using {@link publicDecrypt}.
     *
     * If `privateKey` is not a `KeyObject`, this function behaves as if `privateKey` had been passed to {@link createPrivateKey}. If it is an
     * object, the `padding` property can be passed. Otherwise, this function uses `RSA_PKCS1_PADDING`.
     * @since v1.1.0
     */
    function privateEncrypt(privateKey: RsaPrivateKey | KeyLike, buffer: NodeJS.ArrayBufferView | string): Buffer;
    /**
     * ```js
     * const {
     *   getCiphers,
     * } = await import('node:crypto');
     *
     * console.log(getCiphers()); // ['aes-128-cbc', 'aes-128-ccm', ...]
     * ```
     * @since v0.9.3
     * @return An array with the names of the supported cipher algorithms.
     */
    function getCiphers(): string[];
    /**
     * ```js
     * const {
     *   getCurves,
     * } = await import('node:crypto');
     *
     * console.log(getCurves()); // ['Oakley-EC2N-3', 'Oakley-EC2N-4', ...]
     * ```
     * @since v2.3.0
     * @return An array with the names of the supported elliptic curves.
     */
    function getCurves(): string[];
    /**
     * @since v10.0.0
     * @return `1` if and only if a FIPS compliant crypto provider is currently in use, `0` otherwise. A future semver-major release may change the return type of this API to a {boolean}.
     */
    function getFips(): 1 | 0;
    /**
     * Enables the FIPS compliant crypto provider in a FIPS-enabled Node.js build.
     * Throws an error if FIPS mode is not available.
     * @since v10.0.0
     * @param bool `true` to enable FIPS mode.
     */
    function setFips(bool: boolean): void;
    /**
     * ```js
     * const {
     *   getHashes,
     * } = await import('node:crypto');
     *
     * console.log(getHashes()); // ['DSA', 'DSA-SHA', 'DSA-SHA1', ...]
     * ```
     * @since v0.9.3
     * @return An array of the names of the supported hash algorithms, such as `'RSA-SHA256'`. Hash algorithms are also called "digest" algorithms.
     */
    function getHashes(): string[];
    /**
     * The `ECDH` class is a utility for creating Elliptic Curve Diffie-Hellman (ECDH)
     * key exchanges.
     *
     * Instances of the `ECDH` class can be created using the {@link createECDH} function.
     *
     * ```js
     * import assert from 'node:assert';
     *
     * const {
     *   createECDH,
     * } = await import('node:crypto');
     *
     * // Generate Alice's keys...
     * const alice = createECDH('secp521r1');
     * const aliceKey = alice.generateKeys();
     *
     * // Generate Bob's keys...
     * const bob = createECDH('secp521r1');
     * const bobKey = bob.generateKeys();
     *
     * // Exchange and generate the secret...
     * const aliceSecret = alice.computeSecret(bobKey);
     * const bobSecret = bob.computeSecret(aliceKey);
     *
     * assert.strictEqual(aliceSecret.toString('hex'), bobSecret.toString('hex'));
     * // OK
     * ```
     * @since v0.11.14
     */
    class ECDH {
        private constructor();
        /**
         * Converts the EC Diffie-Hellman public key specified by `key` and `curve` to the
         * format specified by `format`. The `format` argument specifies point encoding
         * and can be `'compressed'`, `'uncompressed'` or `'hybrid'`. The supplied key is
         * interpreted using the specified `inputEncoding`, and the returned key is encoded
         * using the specified `outputEncoding`.
         *
         * Use {@link getCurves} to obtain a list of available curve names.
         * On recent OpenSSL releases, `openssl ecparam -list_curves` will also display
         * the name and description of each available elliptic curve.
         *
         * If `format` is not specified the point will be returned in `'uncompressed'` format.
         *
         * If the `inputEncoding` is not provided, `key` is expected to be a `Buffer`, `TypedArray`, or `DataView`.
         *
         * Example (uncompressing a key):
         *
         * ```js
         * const {
         *   createECDH,
         *   ECDH,
         * } = await import('node:crypto');
         *
         * const ecdh = createECDH('secp256k1');
         * ecdh.generateKeys();
         *
         * const compressedKey = ecdh.getPublicKey('hex', 'compressed');
         *
         * const uncompressedKey = ECDH.convertKey(compressedKey,
         *                                         'secp256k1',
         *                                         'hex',
         *                                         'hex',
         *                                         'uncompressed');
         *
         * // The converted key and the uncompressed public key should be the same
         * console.log(uncompressedKey === ecdh.getPublicKey('hex'));
         * ```
         * @since v10.0.0
         * @param inputEncoding The `encoding` of the `key` string.
         * @param outputEncoding The `encoding` of the return value.
         * @param [format='uncompressed']
         */
        static convertKey(
            key: BinaryLike,
            curve: string,
            inputEncoding?: BinaryToTextEncoding,
            outputEncoding?: "latin1" | "hex" | "base64" | "base64url",
            format?: "uncompressed" | "compressed" | "hybrid",
        ): Buffer | string;
        /**
         * Generates private and public EC Diffie-Hellman key values, and returns
         * the public key in the specified `format` and `encoding`. This key should be
         * transferred to the other party.
         *
         * The `format` argument specifies point encoding and can be `'compressed'` or `'uncompressed'`. If `format` is not specified, the point will be returned in`'uncompressed'` format.
         *
         * If `encoding` is provided a string is returned; otherwise a `Buffer` is returned.
         * @since v0.11.14
         * @param encoding The `encoding` of the return value.
         * @param [format='uncompressed']
         */
        generateKeys(): Buffer;
        generateKeys(encoding: BinaryToTextEncoding, format?: ECDHKeyFormat): string;
        /**
         * Computes the shared secret using `otherPublicKey` as the other
         * party's public key and returns the computed shared secret. The supplied
         * key is interpreted using specified `inputEncoding`, and the returned secret
         * is encoded using the specified `outputEncoding`.
         * If the `inputEncoding` is not
         * provided, `otherPublicKey` is expected to be a `Buffer`, `TypedArray`, or `DataView`.
         *
         * If `outputEncoding` is given a string will be returned; otherwise a `Buffer` is returned.
         *
         * `ecdh.computeSecret` will throw an`ERR_CRYPTO_ECDH_INVALID_PUBLIC_KEY` error when `otherPublicKey` lies outside of the elliptic curve. Since `otherPublicKey` is
         * usually supplied from a remote user over an insecure network,
         * be sure to handle this exception accordingly.
         * @since v0.11.14
         * @param inputEncoding The `encoding` of the `otherPublicKey` string.
         * @param outputEncoding The `encoding` of the return value.
         */
        computeSecret(otherPublicKey: NodeJS.ArrayBufferView): Buffer;
        computeSecret(otherPublicKey: string, inputEncoding: BinaryToTextEncoding): Buffer;
        computeSecret(otherPublicKey: NodeJS.ArrayBufferView, outputEncoding: BinaryToTextEncoding): string;
        computeSecret(
            otherPublicKey: string,
            inputEncoding: BinaryToTextEncoding,
            outputEncoding: BinaryToTextEncoding,
        ): string;
        /**
         * If `encoding` is specified, a string is returned; otherwise a `Buffer` is
         * returned.
         * @since v0.11.14
         * @param encoding The `encoding` of the return value.
         * @return The EC Diffie-Hellman in the specified `encoding`.
         */
        getPrivateKey(): Buffer;
        getPrivateKey(encoding: BinaryToTextEncoding): string;
        /**
         * The `format` argument specifies point encoding and can be `'compressed'` or `'uncompressed'`. If `format` is not specified the point will be returned in`'uncompressed'` format.
         *
         * If `encoding` is specified, a string is returned; otherwise a `Buffer` is
         * returned.
         * @since v0.11.14
         * @param encoding The `encoding` of the return value.
         * @param [format='uncompressed']
         * @return The EC Diffie-Hellman public key in the specified `encoding` and `format`.
         */
        getPublicKey(encoding?: null, format?: ECDHKeyFormat): Buffer;
        getPublicKey(encoding: BinaryToTextEncoding, format?: ECDHKeyFormat): string;
        /**
         * Sets the EC Diffie-Hellman private key.
         * If `encoding` is provided, `privateKey` is expected
         * to be a string; otherwise `privateKey` is expected to be a `Buffer`, `TypedArray`, or `DataView`.
         *
         * If `privateKey` is not valid for the curve specified when the `ECDH` object was
         * created, an error is thrown. Upon setting the private key, the associated
         * public point (key) is also generated and set in the `ECDH` object.
         * @since v0.11.14
         * @param encoding The `encoding` of the `privateKey` string.
         */
        setPrivateKey(privateKey: NodeJS.ArrayBufferView): void;
        setPrivateKey(privateKey: string, encoding: BinaryToTextEncoding): void;
    }
    /**
     * Creates an Elliptic Curve Diffie-Hellman (`ECDH`) key exchange object using a
     * predefined curve specified by the `curveName` string. Use {@link getCurves} to obtain a list of available curve names. On recent
     * OpenSSL releases, `openssl ecparam -list_curves` will also display the name
     * and description of each available elliptic curve.
     * @since v0.11.14
     */
    function createECDH(curveName: string): ECDH;
    /**
     * This function compares the underlying bytes that represent the given `ArrayBuffer`, `TypedArray`, or `DataView` instances using a constant-time
     * algorithm.
     *
     * This function does not leak timing information that
     * would allow an attacker to guess one of the values. This is suitable for
     * comparing HMAC digests or secret values like authentication cookies or [capability urls](https://www.w3.org/TR/capability-urls/).
     *
     * `a` and `b` must both be `Buffer`s, `TypedArray`s, or `DataView`s, and they
     * must have the same byte length. An error is thrown if `a` and `b` have
     * different byte lengths.
     *
     * If at least one of `a` and `b` is a `TypedArray` with more than one byte per
     * entry, such as `Uint16Array`, the result will be computed using the platform
     * byte order.
     *
     * **When both of the inputs are `Float32Array`s or `Float64Array`s, this function might return unexpected results due to IEEE 754**
     * **encoding of floating-point numbers. In particular, neither `x === y` nor `Object.is(x, y)` implies that the byte representations of two floating-point**
     * **numbers `x` and `y` are equal.**
     *
     * Use of `crypto.timingSafeEqual` does not guarantee that the _surrounding_ code
     * is timing-safe. Care should be taken to ensure that the surrounding code does
     * not introduce timing vulnerabilities.
     * @since v6.6.0
     */
    function timingSafeEqual(a: NodeJS.ArrayBufferView, b: NodeJS.ArrayBufferView): boolean;
    type KeyType = "rsa" | "rsa-pss" | "dsa" | "ec" | "ed25519" | "ed448" | "x25519" | "x448";
    type KeyFormat = "pem" | "der" | "jwk";
    interface BasePrivateKeyEncodingOptions<T extends KeyFormat> {
        format: T;
        cipher?: string | undefined;
        passphrase?: string | undefined;
    }
    interface KeyPairKeyObjectResult {
        publicKey: KeyObject;
        privateKey: KeyObject;
    }
    interface ED25519KeyPairKeyObjectOptions {}
    interface ED448KeyPairKeyObjectOptions {}
    interface X25519KeyPairKeyObjectOptions {}
    interface X448KeyPairKeyObjectOptions {}
    interface ECKeyPairKeyObjectOptions {
        /**
         * Name of the curve to use
         */
        namedCurve: string;
        /**
         * Must be `'named'` or `'explicit'`. Default: `'named'`.
         */
        paramEncoding?: "explicit" | "named" | undefined;
    }
    interface RSAKeyPairKeyObjectOptions {
        /**
         * Key size in bits
         */
        modulusLength: number;
        /**
         * Public exponent
         * @default 0x10001
         */
        publicExponent?: number | undefined;
    }
    interface RSAPSSKeyPairKeyObjectOptions {
        /**
         * Key size in bits
         */
        modulusLength: number;
        /**
         * Public exponent
         * @default 0x10001
         */
        publicExponent?: number | undefined;
        /**
         * Name of the message digest
         */
        hashAlgorithm?: string;
        /**
         * Name of the message digest used by MGF1
         */
        mgf1HashAlgorithm?: string;
        /**
         * Minimal salt length in bytes
         */
        saltLength?: string;
    }
    interface DSAKeyPairKeyObjectOptions {
        /**
         * Key size in bits
         */
        modulusLength: number;
        /**
         * Size of q in bits
         */
        divisorLength: number;
    }
    interface RSAKeyPairOptions<PubF extends KeyFormat, PrivF extends KeyFormat> {
        /**
         * Key size in bits
         */
        modulusLength: number;
        /**
         * Public exponent
         * @default 0x10001
         */
        publicExponent?: number | undefined;
        publicKeyEncoding: {
            type: "pkcs1" | "spki";
            format: PubF;
        };
        privateKeyEncoding: BasePrivateKeyEncodingOptions<PrivF> & {
            type: "pkcs1" | "pkcs8";
        };
    }
    interface RSAPSSKeyPairOptions<PubF extends KeyFormat, PrivF extends KeyFormat> {
        /**
         * Key size in bits
         */
        modulusLength: number;
        /**
         * Public exponent
         * @default 0x10001
         */
        publicExponent?: number | undefined;
        /**
         * Name of the message digest
         */
        hashAlgorithm?: string;
        /**
         * Name of the message digest used by MGF1
         */
        mgf1HashAlgorithm?: string;
        /**
         * Minimal salt length in bytes
         */
        saltLength?: string;
        publicKeyEncoding: {
            type: "spki";
            format: PubF;
        };
        privateKeyEncoding: BasePrivateKeyEncodingOptions<PrivF> & {
            type: "pkcs8";
        };
    }
    interface DSAKeyPairOptions<PubF extends KeyFormat, PrivF extends KeyFormat> {
        /**
         * Key size in bits
         */
        modulusLength: number;
        /**
         * Size of q in bits
         */
        divisorLength: number;
        publicKeyEncoding: {
            type: "spki";
            format: PubF;
        };
        privateKeyEncoding: BasePrivateKeyEncodingOptions<PrivF> & {
            type: "pkcs8";
        };
    }
    interface ECKeyPairOptions<PubF extends KeyFormat, PrivF extends KeyFormat> extends ECKeyPairKeyObjectOptions {
        publicKeyEncoding: {
            type: "pkcs1" | "spki";
            format: PubF;
        };
        privateKeyEncoding: BasePrivateKeyEncodingOptions<PrivF> & {
            type: "sec1" | "pkcs8";
        };
    }
    interface ED25519KeyPairOptions<PubF extends KeyFormat, PrivF extends KeyFormat> {
        publicKeyEncoding: {
            type: "spki";
            format: PubF;
        };
        privateKeyEncoding: BasePrivateKeyEncodingOptions<PrivF> & {
            type: "pkcs8";
        };
    }
    interface ED448KeyPairOptions<PubF extends KeyFormat, PrivF extends KeyFormat> {
        publicKeyEncoding: {
            type: "spki";
            format: PubF;
        };
        privateKeyEncoding: BasePrivateKeyEncodingOptions<PrivF> & {
            type: "pkcs8";
        };
    }
    interface X25519KeyPairOptions<PubF extends KeyFormat, PrivF extends KeyFormat> {
        publicKeyEncoding: {
            type: "spki";
            format: PubF;
        };
        privateKeyEncoding: BasePrivateKeyEncodingOptions<PrivF> & {
            type: "pkcs8";
        };
    }
    interface X448KeyPairOptions<PubF extends KeyFormat, PrivF extends KeyFormat> {
        publicKeyEncoding: {
            type: "spki";
            format: PubF;
        };
        privateKeyEncoding: BasePrivateKeyEncodingOptions<PrivF> & {
            type: "pkcs8";
        };
    }
    interface KeyPairSyncResult<T1 extends string | Buffer, T2 extends string | Buffer> {
        publicKey: T1;
        privateKey: T2;
    }
    /**
     * Generates a new asymmetric key pair of the given `type`. RSA, RSA-PSS, DSA, EC,
     * Ed25519, Ed448, X25519, X448, and DH are currently supported.
     *
     * If a `publicKeyEncoding` or `privateKeyEncoding` was specified, this function
     * behaves as if `keyObject.export()` had been called on its result. Otherwise,
     * the respective part of the key is returned as a `KeyObject`.
     *
     * When encoding public keys, it is recommended to use `'spki'`. When encoding
     * private keys, it is recommended to use `'pkcs8'` with a strong passphrase,
     * and to keep the passphrase confidential.
     *
     * ```js
     * const {
     *   generateKeyPairSync,
     * } = await import('node:crypto');
     *
     * const {
     *   publicKey,
     *   privateKey,
     * } = generateKeyPairSync('rsa', {
     *   modulusLength: 4096,
     *   publicKeyEncoding: {
     *     type: 'spki',
     *     format: 'pem',
     *   },
     *   privateKeyEncoding: {
     *     type: 'pkcs8',
     *     format: 'pem',
     *     cipher: 'aes-256-cbc',
     *     passphrase: 'top secret',
     *   },
     * });
     * ```
     *
     * The return value `{ publicKey, privateKey }` represents the generated key pair.
     * When PEM encoding was selected, the respective key will be a string, otherwise
     * it will be a buffer containing the data encoded as DER.
     * @since v10.12.0
     * @param type Must be `'rsa'`, `'rsa-pss'`, `'dsa'`, `'ec'`, `'ed25519'`, `'ed448'`, `'x25519'`, `'x448'`, or `'dh'`.
     */
    function generateKeyPairSync(
        type: "rsa",
        options: RSAKeyPairOptions<"pem", "pem">,
    ): KeyPairSyncResult<string, string>;
    function generateKeyPairSync(
        type: "rsa",
        options: RSAKeyPairOptions<"pem", "der">,
    ): KeyPairSyncResult<string, Buffer>;
    function generateKeyPairSync(
        type: "rsa",
        options: RSAKeyPairOptions<"der", "pem">,
    ): KeyPairSyncResult<Buffer, string>;
    function generateKeyPairSync(
        type: "rsa",
        options: RSAKeyPairOptions<"der", "der">,
    ): KeyPairSyncResult<Buffer, Buffer>;
    function generateKeyPairSync(type: "rsa", options: RSAKeyPairKeyObjectOptions): KeyPairKeyObjectResult;
    function generateKeyPairSync(
        type: "rsa-pss",
        options: RSAPSSKeyPairOptions<"pem", "pem">,
    ): KeyPairSyncResult<string, string>;
    function generateKeyPairSync(
        type: "rsa-pss",
        options: RSAPSSKeyPairOptions<"pem", "der">,
    ): KeyPairSyncResult<string, Buffer>;
    function generateKeyPairSync(
        type: "rsa-pss",
        options: RSAPSSKeyPairOptions<"der", "pem">,
    ): KeyPairSyncResult<Buffer, string>;
    function generateKeyPairSync(
        type: "rsa-pss",
        options: RSAPSSKeyPairOptions<"der", "der">,
    ): KeyPairSyncResult<Buffer, Buffer>;
    function generateKeyPairSync(type: "rsa-pss", options: RSAPSSKeyPairKeyObjectOptions): KeyPairKeyObjectResult;
    function generateKeyPairSync(
        type: "dsa",
        options: DSAKeyPairOptions<"pem", "pem">,
    ): KeyPairSyncResult<string, string>;
    function generateKeyPairSync(
        type: "dsa",
        options: DSAKeyPairOptions<"pem", "der">,
    ): KeyPairSyncResult<string, Buffer>;
    function generateKeyPairSync(
        type: "dsa",
        options: DSAKeyPairOptions<"der", "pem">,
    ): KeyPairSyncResult<Buffer, string>;
    function generateKeyPairSync(
        type: "dsa",
        options: DSAKeyPairOptions<"der", "der">,
    ): KeyPairSyncResult<Buffer, Buffer>;
    function generateKeyPairSync(type: "dsa", options: DSAKeyPairKeyObjectOptions): KeyPairKeyObjectResult;
    function generateKeyPairSync(
        type: "ec",
        options: ECKeyPairOptions<"pem", "pem">,
    ): KeyPairSyncResult<string, string>;
    function generateKeyPairSync(
        type: "ec",
        options: ECKeyPairOptions<"pem", "der">,
    ): KeyPairSyncResult<string, Buffer>;
    function generateKeyPairSync(
        type: "ec",
        options: ECKeyPairOptions<"der", "pem">,
    ): KeyPairSyncResult<Buffer, string>;
    function generateKeyPairSync(
        type: "ec",
        options: ECKeyPairOptions<"der", "der">,
    ): KeyPairSyncResult<Buffer, Buffer>;
    function generateKeyPairSync(type: "ec", options: ECKeyPairKeyObjectOptions): KeyPairKeyObjectResult;
    function generateKeyPairSync(
        type: "ed25519",
        options: ED25519KeyPairOptions<"pem", "pem">,
    ): KeyPairSyncResult<string, string>;
    function generateKeyPairSync(
        type: "ed25519",
        options: ED25519KeyPairOptions<"pem", "der">,
    ): KeyPairSyncResult<string, Buffer>;
    function generateKeyPairSync(
        type: "ed25519",
        options: ED25519KeyPairOptions<"der", "pem">,
    ): KeyPairSyncResult<Buffer, string>;
    function generateKeyPairSync(
        type: "ed25519",
        options: ED25519KeyPairOptions<"der", "der">,
    ): KeyPairSyncResult<Buffer, Buffer>;
    function generateKeyPairSync(type: "ed25519", options?: ED25519KeyPairKeyObjectOptions): KeyPairKeyObjectResult;
    function generateKeyPairSync(
        type: "ed448",
        options: ED448KeyPairOptions<"pem", "pem">,
    ): KeyPairSyncResult<string, string>;
    function generateKeyPairSync(
        type: "ed448",
        options: ED448KeyPairOptions<"pem", "der">,
    ): KeyPairSyncResult<string, Buffer>;
    function generateKeyPairSync(
        type: "ed448",
        options: ED448KeyPairOptions<"der", "pem">,
    ): KeyPairSyncResult<Buffer, string>;
    function generateKeyPairSync(
        type: "ed448",
        options: ED448KeyPairOptions<"der", "der">,
    ): KeyPairSyncResult<Buffer, Buffer>;
    function generateKeyPairSync(type: "ed448", options?: ED448KeyPairKeyObjectOptions): KeyPairKeyObjectResult;
    function generateKeyPairSync(
        type: "x25519",
        options: X25519KeyPairOptions<"pem", "pem">,
    ): KeyPairSyncResult<string, string>;
    function generateKeyPairSync(
        type: "x25519",
        options: X25519KeyPairOptions<"pem", "der">,
    ): KeyPairSyncResult<string, Buffer>;
    function generateKeyPairSync(
        type: "x25519",
        options: X25519KeyPairOptions<"der", "pem">,
    ): KeyPairSyncResult<Buffer, string>;
    function generateKeyPairSync(
        type: "x25519",
        options: X25519KeyPairOptions<"der", "der">,
    ): KeyPairSyncResult<Buffer, Buffer>;
    function generateKeyPairSync(type: "x25519", options?: X25519KeyPairKeyObjectOptions): KeyPairKeyObjectResult;
    function generateKeyPairSync(
        type: "x448",
        options: X448KeyPairOptions<"pem", "pem">,
    ): KeyPairSyncResult<string, string>;
    function generateKeyPairSync(
        type: "x448",
        options: X448KeyPairOptions<"pem", "der">,
    ): KeyPairSyncResult<string, Buffer>;
    function generateKeyPairSync(
        type: "x448",
        options: X448KeyPairOptions<"der", "pem">,
    ): KeyPairSyncResult<Buffer, string>;
    function generateKeyPairSync(
        type: "x448",
        options: X448KeyPairOptions<"der", "der">,
    ): KeyPairSyncResult<Buffer, Buffer>;
    function generateKeyPairSync(type: "x448", options?: X448KeyPairKeyObjectOptions): KeyPairKeyObjectResult;
    /**
     * Generates a new asymmetric key pair of the given `type`. RSA, RSA-PSS, DSA, EC,
     * Ed25519, Ed448, X25519, X448, and DH are currently supported.
     *
     * If a `publicKeyEncoding` or `privateKeyEncoding` was specified, this function
     * behaves as if `keyObject.export()` had been called on its result. Otherwise,
     * the respective part of the key is returned as a `KeyObject`.
     *
     * It is recommended to encode public keys as `'spki'` and private keys as `'pkcs8'` with encryption for long-term storage:
     *
     * ```js
     * const {
     *   generateKeyPair,
     * } = await import('node:crypto');
     *
     * generateKeyPair('rsa', {
     *   modulusLength: 4096,
     *   publicKeyEncoding: {
     *     type: 'spki',
     *     format: 'pem',
     *   },
     *   privateKeyEncoding: {
     *     type: 'pkcs8',
     *     format: 'pem',
     *     cipher: 'aes-256-cbc',
     *     passphrase: 'top secret',
     *   },
     * }, (err, publicKey, privateKey) => {
     *   // Handle errors and use the generated key pair.
     * });
     * ```
     *
     * On completion, `callback` will be called with `err` set to `undefined` and `publicKey` / `privateKey` representing the generated key pair.
     *
     * If this method is invoked as its `util.promisify()` ed version, it returns
     * a `Promise` for an `Object` with `publicKey` and `privateKey` properties.
     * @since v10.12.0
     * @param type Must be `'rsa'`, `'rsa-pss'`, `'dsa'`, `'ec'`, `'ed25519'`, `'ed448'`, `'x25519'`, `'x448'`, or `'dh'`.
     */
    function generateKeyPair(
        type: "rsa",
        options: RSAKeyPairOptions<"pem", "pem">,
        callback: (err: Error | null, publicKey: string, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "rsa",
        options: RSAKeyPairOptions<"pem", "der">,
        callback: (err: Error | null, publicKey: string, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "rsa",
        options: RSAKeyPairOptions<"der", "pem">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "rsa",
        options: RSAKeyPairOptions<"der", "der">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "rsa",
        options: RSAKeyPairKeyObjectOptions,
        callback: (err: Error | null, publicKey: KeyObject, privateKey: KeyObject) => void,
    ): void;
    function generateKeyPair(
        type: "rsa-pss",
        options: RSAPSSKeyPairOptions<"pem", "pem">,
        callback: (err: Error | null, publicKey: string, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "rsa-pss",
        options: RSAPSSKeyPairOptions<"pem", "der">,
        callback: (err: Error | null, publicKey: string, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "rsa-pss",
        options: RSAPSSKeyPairOptions<"der", "pem">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "rsa-pss",
        options: RSAPSSKeyPairOptions<"der", "der">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "rsa-pss",
        options: RSAPSSKeyPairKeyObjectOptions,
        callback: (err: Error | null, publicKey: KeyObject, privateKey: KeyObject) => void,
    ): void;
    function generateKeyPair(
        type: "dsa",
        options: DSAKeyPairOptions<"pem", "pem">,
        callback: (err: Error | null, publicKey: string, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "dsa",
        options: DSAKeyPairOptions<"pem", "der">,
        callback: (err: Error | null, publicKey: string, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "dsa",
        options: DSAKeyPairOptions<"der", "pem">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "dsa",
        options: DSAKeyPairOptions<"der", "der">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "dsa",
        options: DSAKeyPairKeyObjectOptions,
        callback: (err: Error | null, publicKey: KeyObject, privateKey: KeyObject) => void,
    ): void;
    function generateKeyPair(
        type: "ec",
        options: ECKeyPairOptions<"pem", "pem">,
        callback: (err: Error | null, publicKey: string, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "ec",
        options: ECKeyPairOptions<"pem", "der">,
        callback: (err: Error | null, publicKey: string, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "ec",
        options: ECKeyPairOptions<"der", "pem">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "ec",
        options: ECKeyPairOptions<"der", "der">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "ec",
        options: ECKeyPairKeyObjectOptions,
        callback: (err: Error | null, publicKey: KeyObject, privateKey: KeyObject) => void,
    ): void;
    function generateKeyPair(
        type: "ed25519",
        options: ED25519KeyPairOptions<"pem", "pem">,
        callback: (err: Error | null, publicKey: string, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "ed25519",
        options: ED25519KeyPairOptions<"pem", "der">,
        callback: (err: Error | null, publicKey: string, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "ed25519",
        options: ED25519KeyPairOptions<"der", "pem">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "ed25519",
        options: ED25519KeyPairOptions<"der", "der">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "ed25519",
        options: ED25519KeyPairKeyObjectOptions | undefined,
        callback: (err: Error | null, publicKey: KeyObject, privateKey: KeyObject) => void,
    ): void;
    function generateKeyPair(
        type: "ed448",
        options: ED448KeyPairOptions<"pem", "pem">,
        callback: (err: Error | null, publicKey: string, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "ed448",
        options: ED448KeyPairOptions<"pem", "der">,
        callback: (err: Error | null, publicKey: string, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "ed448",
        options: ED448KeyPairOptions<"der", "pem">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "ed448",
        options: ED448KeyPairOptions<"der", "der">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "ed448",
        options: ED448KeyPairKeyObjectOptions | undefined,
        callback: (err: Error | null, publicKey: KeyObject, privateKey: KeyObject) => void,
    ): void;
    function generateKeyPair(
        type: "x25519",
        options: X25519KeyPairOptions<"pem", "pem">,
        callback: (err: Error | null, publicKey: string, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "x25519",
        options: X25519KeyPairOptions<"pem", "der">,
        callback: (err: Error | null, publicKey: string, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "x25519",
        options: X25519KeyPairOptions<"der", "pem">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "x25519",
        options: X25519KeyPairOptions<"der", "der">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "x25519",
        options: X25519KeyPairKeyObjectOptions | undefined,
        callback: (err: Error | null, publicKey: KeyObject, privateKey: KeyObject) => void,
    ): void;
    function generateKeyPair(
        type: "x448",
        options: X448KeyPairOptions<"pem", "pem">,
        callback: (err: Error | null, publicKey: string, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "x448",
        options: X448KeyPairOptions<"pem", "der">,
        callback: (err: Error | null, publicKey: string, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "x448",
        options: X448KeyPairOptions<"der", "pem">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: string) => void,
    ): void;
    function generateKeyPair(
        type: "x448",
        options: X448KeyPairOptions<"der", "der">,
        callback: (err: Error | null, publicKey: Buffer, privateKey: Buffer) => void,
    ): void;
    function generateKeyPair(
        type: "x448",
        options: X448KeyPairKeyObjectOptions | undefined,
        callback: (err: Error | null, publicKey: KeyObject, privateKey: KeyObject) => void,
    ): void;
    namespace generateKeyPair {
        function __promisify__(
            type: "rsa",
            options: RSAKeyPairOptions<"pem", "pem">,
        ): Promise<{
            publicKey: string;
            privateKey: string;
        }>;
        function __promisify__(
            type: "rsa",
            options: RSAKeyPairOptions<"pem", "der">,
        ): Promise<{
            publicKey: string;
            privateKey: Buffer;
        }>;
        function __promisify__(
            type: "rsa",
            options: RSAKeyPairOptions<"der", "pem">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: string;
        }>;
        function __promisify__(
            type: "rsa",
            options: RSAKeyPairOptions<"der", "der">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: Buffer;
        }>;
        function __promisify__(type: "rsa", options: RSAKeyPairKeyObjectOptions): Promise<KeyPairKeyObjectResult>;
        function __promisify__(
            type: "rsa-pss",
            options: RSAPSSKeyPairOptions<"pem", "pem">,
        ): Promise<{
            publicKey: string;
            privateKey: string;
        }>;
        function __promisify__(
            type: "rsa-pss",
            options: RSAPSSKeyPairOptions<"pem", "der">,
        ): Promise<{
            publicKey: string;
            privateKey: Buffer;
        }>;
        function __promisify__(
            type: "rsa-pss",
            options: RSAPSSKeyPairOptions<"der", "pem">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: string;
        }>;
        function __promisify__(
            type: "rsa-pss",
            options: RSAPSSKeyPairOptions<"der", "der">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: Buffer;
        }>;
        function __promisify__(
            type: "rsa-pss",
            options: RSAPSSKeyPairKeyObjectOptions,
        ): Promise<KeyPairKeyObjectResult>;
        function __promisify__(
            type: "dsa",
            options: DSAKeyPairOptions<"pem", "pem">,
        ): Promise<{
            publicKey: string;
            privateKey: string;
        }>;
        function __promisify__(
            type: "dsa",
            options: DSAKeyPairOptions<"pem", "der">,
        ): Promise<{
            publicKey: string;
            privateKey: Buffer;
        }>;
        function __promisify__(
            type: "dsa",
            options: DSAKeyPairOptions<"der", "pem">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: string;
        }>;
        function __promisify__(
            type: "dsa",
            options: DSAKeyPairOptions<"der", "der">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: Buffer;
        }>;
        function __promisify__(type: "dsa", options: DSAKeyPairKeyObjectOptions): Promise<KeyPairKeyObjectResult>;
        function __promisify__(
            type: "ec",
            options: ECKeyPairOptions<"pem", "pem">,
        ): Promise<{
            publicKey: string;
            privateKey: string;
        }>;
        function __promisify__(
            type: "ec",
            options: ECKeyPairOptions<"pem", "der">,
        ): Promise<{
            publicKey: string;
            privateKey: Buffer;
        }>;
        function __promisify__(
            type: "ec",
            options: ECKeyPairOptions<"der", "pem">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: string;
        }>;
        function __promisify__(
            type: "ec",
            options: ECKeyPairOptions<"der", "der">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: Buffer;
        }>;
        function __promisify__(type: "ec", options: ECKeyPairKeyObjectOptions): Promise<KeyPairKeyObjectResult>;
        function __promisify__(
            type: "ed25519",
            options: ED25519KeyPairOptions<"pem", "pem">,
        ): Promise<{
            publicKey: string;
            privateKey: string;
        }>;
        function __promisify__(
            type: "ed25519",
            options: ED25519KeyPairOptions<"pem", "der">,
        ): Promise<{
            publicKey: string;
            privateKey: Buffer;
        }>;
        function __promisify__(
            type: "ed25519",
            options: ED25519KeyPairOptions<"der", "pem">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: string;
        }>;
        function __promisify__(
            type: "ed25519",
            options: ED25519KeyPairOptions<"der", "der">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: Buffer;
        }>;
        function __promisify__(
            type: "ed25519",
            options?: ED25519KeyPairKeyObjectOptions,
        ): Promise<KeyPairKeyObjectResult>;
        function __promisify__(
            type: "ed448",
            options: ED448KeyPairOptions<"pem", "pem">,
        ): Promise<{
            publicKey: string;
            privateKey: string;
        }>;
        function __promisify__(
            type: "ed448",
            options: ED448KeyPairOptions<"pem", "der">,
        ): Promise<{
            publicKey: string;
            privateKey: Buffer;
        }>;
        function __promisify__(
            type: "ed448",
            options: ED448KeyPairOptions<"der", "pem">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: string;
        }>;
        function __promisify__(
            type: "ed448",
            options: ED448KeyPairOptions<"der", "der">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: Buffer;
        }>;
        function __promisify__(type: "ed448", options?: ED448KeyPairKeyObjectOptions): Promise<KeyPairKeyObjectResult>;
        function __promisify__(
            type: "x25519",
            options: X25519KeyPairOptions<"pem", "pem">,
        ): Promise<{
            publicKey: string;
            privateKey: string;
        }>;
        function __promisify__(
            type: "x25519",
            options: X25519KeyPairOptions<"pem", "der">,
        ): Promise<{
            publicKey: string;
            privateKey: Buffer;
        }>;
        function __promisify__(
            type: "x25519",
            options: X25519KeyPairOptions<"der", "pem">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: string;
        }>;
        function __promisify__(
            type: "x25519",
            options: X25519KeyPairOptions<"der", "der">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: Buffer;
        }>;
        function __promisify__(
            type: "x25519",
            options?: X25519KeyPairKeyObjectOptions,
        ): Promise<KeyPairKeyObjectResult>;
        function __promisify__(
            type: "x448",
            options: X448KeyPairOptions<"pem", "pem">,
        ): Promise<{
            publicKey: string;
            privateKey: string;
        }>;
        function __promisify__(
            type: "x448",
            options: X448KeyPairOptions<"pem", "der">,
        ): Promise<{
            publicKey: string;
            privateKey: Buffer;
        }>;
        function __promisify__(
            type: "x448",
            options: X448KeyPairOptions<"der", "pem">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: string;
        }>;
        function __promisify__(
            type: "x448",
            options: X448KeyPairOptions<"der", "der">,
        ): Promise<{
            publicKey: Buffer;
            privateKey: Buffer;
        }>;
        function __promisify__(type: "x448", options?: X448KeyPairKeyObjectOptions): Promise<KeyPairKeyObjectResult>;
    }
    /**
     * Calculates and returns the signature for `data` using the given private key and
     * algorithm. If `algorithm` is `null` or `undefined`, then the algorithm is
     * dependent upon the key type (especially Ed25519 and Ed448).
     *
     * If `key` is not a `KeyObject`, this function behaves as if `key` had been
     * passed to {@link createPrivateKey}. If it is an object, the following
     * additional properties can be passed:
     *
     * If the `callback` function is provided this function uses libuv's threadpool.
     * @since v12.0.0
     */
    function sign(
        algorithm: string | null | undefined,
        data: NodeJS.ArrayBufferView,
        key: KeyLike | SignKeyObjectInput | SignPrivateKeyInput | SignJsonWebKeyInput,
    ): Buffer;
    function sign(
        algorithm: string | null | undefined,
        data: NodeJS.ArrayBufferView,
        key: KeyLike | SignKeyObjectInput | SignPrivateKeyInput | SignJsonWebKeyInput,
        callback: (error: Error | null, data: Buffer) => void,
    ): void;
    /**
     * Verifies the given signature for `data` using the given key and algorithm. If `algorithm` is `null` or `undefined`, then the algorithm is dependent upon the
     * key type (especially Ed25519 and Ed448).
     *
     * If `key` is not a `KeyObject`, this function behaves as if `key` had been
     * passed to {@link createPublicKey}. If it is an object, the following
     * additional properties can be passed:
     *
     * The `signature` argument is the previously calculated signature for the `data`.
     *
     * Because public keys can be derived from private keys, a private key or a public
     * key may be passed for `key`.
     *
     * If the `callback` function is provided this function uses libuv's threadpool.
     * @since v12.0.0
     */
    function verify(
        algorithm: string | null | undefined,
        data: NodeJS.ArrayBufferView,
        key: KeyLike | VerifyKeyObjectInput | VerifyPublicKeyInput | VerifyJsonWebKeyInput,
        signature: NodeJS.ArrayBufferView,
    ): boolean;
    function verify(
        algorithm: string | null | undefined,
        data: NodeJS.ArrayBufferView,
        key: KeyLike | VerifyKeyObjectInput | VerifyPublicKeyInput | VerifyJsonWebKeyInput,
        signature: NodeJS.ArrayBufferView,
        callback: (error: Error | null, result: boolean) => void,
    ): void;
    /**
     * Computes the Diffie-Hellman secret based on a `privateKey` and a `publicKey`.
     * Both keys must have the same `asymmetricKeyType`, which must be one of `'dh'` (for Diffie-Hellman), `'ec'` (for ECDH), `'x448'`, or `'x25519'` (for ECDH-ES).
     * @since v13.9.0, v12.17.0
     */
    function diffieHellman(options: { privateKey: KeyObject; publicKey: KeyObject }): Buffer;
    /**
     * A utility for creating one-shot hash digests of data. It can be faster than the object-based `crypto.createHash()` when hashing a smaller amount of data
     * (<= 5MB) that's readily available. If the data can be big or if it is streamed, it's still recommended to use `crypto.createHash()` instead. The `algorithm`
     * is dependent on the available algorithms supported by the version of OpenSSL on the platform. Examples are `'sha256'`, `'sha512'`, etc. On recent releases
     * of OpenSSL, `openssl list -digest-algorithms` will display the available digest algorithms.
     *
     * Example:
     *
     * ```js
     * import crypto from 'node:crypto';
     * import { Buffer } from 'node:buffer';
     *
     * // Hashing a string and return the result as a hex-encoded string.
     * const string = 'Node.js';
     * // 10b3493287f831e81a438811a1ffba01f8cec4b7
     * console.log(crypto.hash('sha1', string));
     *
     * // Encode a base64-encoded string into a Buffer, hash it and return
     * // the result as a buffer.
     * const base64 = 'Tm9kZS5qcw==';
     * // <Buffer 10 b3 49 32 87 f8 31 e8 1a 43 88 11 a1 ff ba 01 f8 ce c4 b7>
     * console.log(crypto.hash('sha1', Buffer.from(base64, 'base64'), 'buffer'));
     * ```
     * @since v21.7.0, v20.12.0
     * @param data When `data` is a string, it will be encoded as UTF-8 before being hashed. If a different input encoding is desired for a string input, user
     *             could encode the string into a `TypedArray` using either `TextEncoder` or `Buffer.from()` and passing the encoded `TypedArray` into this API instead.
     * @param [outputEncoding='hex'] [Encoding](https://nodejs.org/docs/latest-v22.x/api/buffer.html#buffers-and-character-encodings) used to encode the returned digest.
     */
    function hash(algorithm: string, data: BinaryLike, outputEncoding?: BinaryToTextEncoding): string;
    function hash(algorithm: string, data: BinaryLike, outputEncoding: "buffer"): Buffer;
    function hash(
        algorithm: string,
        data: BinaryLike,
        outputEncoding?: BinaryToTextEncoding | "buffer",
    ): string | Buffer;
    type CipherMode = "cbc" | "ccm" | "cfb" | "ctr" | "ecb" | "gcm" | "ocb" | "ofb" | "stream" | "wrap" | "xts";
    interface CipherInfoOptions {
        /**
         * A test key length.
         */
        keyLength?: number | undefined;
        /**
         * A test IV length.
         */
        ivLength?: number | undefined;
    }
    interface CipherInfo {
        /**
         * The name of the cipher.
         */
        name: string;
        /**
         * The nid of the cipher.
         */
        nid: number;
        /**
         * The block size of the cipher in bytes.
         * This property is omitted when mode is 'stream'.
         */
        blockSize?: number | undefined;
        /**
         * The expected or default initialization vector length in bytes.
         * This property is omitted if the cipher does not use an initialization vector.
         */
        ivLength?: number | undefined;
        /**
         * The expected or default key length in bytes.
         */
        keyLength: number;
        /**
         * The cipher mode.
         */
        mode: CipherMode;
    }
    /**
     * Returns information about a given cipher.
     *
     * Some ciphers accept variable length keys and initialization vectors. By default,
     * the `crypto.getCipherInfo()` method will return the default values for these
     * ciphers. To test if a given key length or iv length is acceptable for given
     * cipher, use the `keyLength` and `ivLength` options. If the given values are
     * unacceptable, `undefined` will be returned.
     * @since v15.0.0
     * @param nameOrNid The name or nid of the cipher to query.
     */
    function getCipherInfo(nameOrNid: string | number, options?: CipherInfoOptions): CipherInfo | undefined;
    /**
     * HKDF is a simple key derivation function defined in RFC 5869\. The given `ikm`, `salt` and `info` are used with the `digest` to derive a key of `keylen` bytes.
     *
     * The supplied `callback` function is called with two arguments: `err` and `derivedKey`. If an errors occurs while deriving the key, `err` will be set;
     * otherwise `err` will be `null`. The successfully generated `derivedKey` will
     * be passed to the callback as an [ArrayBuffer](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/ArrayBuffer). An error will be thrown if any
     * of the input arguments specify invalid values or types.
     *
     * ```js
     * import { Buffer } from 'node:buffer';
     * const {
     *   hkdf,
     * } = await import('node:crypto');
     *
     * hkdf('sha512', 'key', 'salt', 'info', 64, (err, derivedKey) => {
     *   if (err) throw err;
     *   console.log(Buffer.from(derivedKey).toString('hex'));  // '24156e2...5391653'
     * });
     * ```
     * @since v15.0.0
     * @param digest The digest algorithm to use.
     * @param ikm The input keying material. Must be provided but can be zero-length.
     * @param salt The salt value. Must be provided but can be zero-length.
     * @param info Additional info value. Must be provided but can be zero-length, and cannot be more than 1024 bytes.
     * @param keylen The length of the key to generate. Must be greater than 0. The maximum allowable value is `255` times the number of bytes produced by the selected digest function (e.g. `sha512`
     * generates 64-byte hashes, making the maximum HKDF output 16320 bytes).
     */
    function hkdf(
        digest: string,
        irm: BinaryLike | KeyObject,
        salt: BinaryLike,
        info: BinaryLike,
        keylen: number,
        callback: (err: Error | null, derivedKey: ArrayBuffer) => void,
    ): void;
    /**
     * Provides a synchronous HKDF key derivation function as defined in RFC 5869\. The
     * given `ikm`, `salt` and `info` are used with the `digest` to derive a key of `keylen` bytes.
     *
     * The successfully generated `derivedKey` will be returned as an [ArrayBuffer](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/ArrayBuffer).
     *
     * An error will be thrown if any of the input arguments specify invalid values or
     * types, or if the derived key cannot be generated.
     *
     * ```js
     * import { Buffer } from 'node:buffer';
     * const {
     *   hkdfSync,
     * } = await import('node:crypto');
     *
     * const derivedKey = hkdfSync('sha512', 'key', 'salt', 'info', 64);
     * console.log(Buffer.from(derivedKey).toString('hex'));  // '24156e2...5391653'
     * ```
     * @since v15.0.0
     * @param digest The digest algorithm to use.
     * @param ikm The input keying material. Must be provided but can be zero-length.
     * @param salt The salt value. Must be provided but can be zero-length.
     * @param info Additional info value. Must be provided but can be zero-length, and cannot be more than 1024 bytes.
     * @param keylen The length of the key to generate. Must be greater than 0. The maximum allowable value is `255` times the number of bytes produced by the selected digest function (e.g. `sha512`
     * generates 64-byte hashes, making the maximum HKDF output 16320 bytes).
     */
    function hkdfSync(
        digest: string,
        ikm: BinaryLike | KeyObject,
        salt: BinaryLike,
        info: BinaryLike,
        keylen: number,
    ): ArrayBuffer;
    interface SecureHeapUsage {
        /**
         * The total allocated secure heap size as specified using the `--secure-heap=n` command-line flag.
         */
        total: number;
        /**
         * The minimum allocation from the secure heap as specified using the `--secure-heap-min` command-line flag.
         */
        min: number;
        /**
         * The total number of bytes currently allocated from the secure heap.
         */
        used: number;
        /**
         * The calculated ratio of `used` to `total` allocated bytes.
         */
        utilization: number;
    }
    /**
     * @since v15.6.0
     */
    function secureHeapUsed(): SecureHeapUsage;
    interface RandomUUIDOptions {
        /**
         * By default, to improve performance,
         * Node.js will pre-emptively generate and persistently cache enough
         * random data to generate up to 128 random UUIDs. To generate a UUID
         * without using the cache, set `disableEntropyCache` to `true`.
         *
         * @default `false`
         */
        disableEntropyCache?: boolean | undefined;
    }
    type UUID = `${string}-${string}-${string}-${string}-${string}`;
    /**
     * Generates a random [RFC 4122](https://www.rfc-editor.org/rfc/rfc4122.txt) version 4 UUID. The UUID is generated using a
     * cryptographic pseudorandom number generator.
     * @since v15.6.0, v14.17.0
     */
    function randomUUID(options?: RandomUUIDOptions): UUID;
    interface X509CheckOptions {
        /**
         * @default 'always'
         */
        subject?: "always" | "default" | "never";
        /**
         * @default true
         */
        wildcards?: boolean;
        /**
         * @default true
         */
        partialWildcards?: boolean;
        /**
         * @default false
         */
        multiLabelWildcards?: boolean;
        /**
         * @default false
         */
        singleLabelSubdomains?: boolean;
    }
    /**
     * Encapsulates an X509 certificate and provides read-only access to
     * its information.
     *
     * ```js
     * const { X509Certificate } = await import('node:crypto');
     *
     * const x509 = new X509Certificate('{... pem encoded cert ...}');
     *
     * console.log(x509.subject);
     * ```
     * @since v15.6.0
     */
    class X509Certificate {
        /**
         * Will be \`true\` if this is a Certificate Authority (CA) certificate.
         * @since v15.6.0
         */
        readonly ca: boolean;
        /**
         * The SHA-1 fingerprint of this certificate.
         *
         * Because SHA-1 is cryptographically broken and because the security of SHA-1 is
         * significantly worse than that of algorithms that are commonly used to sign
         * certificates, consider using `x509.fingerprint256` instead.
         * @since v15.6.0
         */
        readonly fingerprint: string;
        /**
         * The SHA-256 fingerprint of this certificate.
         * @since v15.6.0
         */
        readonly fingerprint256: string;
        /**
         * The SHA-512 fingerprint of this certificate.
         *
         * Because computing the SHA-256 fingerprint is usually faster and because it is
         * only half the size of the SHA-512 fingerprint, `x509.fingerprint256` may be
         * a better choice. While SHA-512 presumably provides a higher level of security in
         * general, the security of SHA-256 matches that of most algorithms that are
         * commonly used to sign certificates.
         * @since v17.2.0, v16.14.0
         */
        readonly fingerprint512: string;
        /**
         * The complete subject of this certificate.
         * @since v15.6.0
         */
        readonly subject: string;
        /**
         * The subject alternative name specified for this certificate.
         *
         * This is a comma-separated list of subject alternative names. Each entry begins
         * with a string identifying the kind of the subject alternative name followed by
         * a colon and the value associated with the entry.
         *
         * Earlier versions of Node.js incorrectly assumed that it is safe to split this
         * property at the two-character sequence `', '` (see [CVE-2021-44532](https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-44532)). However,
         * both malicious and legitimate certificates can contain subject alternative names
         * that include this sequence when represented as a string.
         *
         * After the prefix denoting the type of the entry, the remainder of each entry
         * might be enclosed in quotes to indicate that the value is a JSON string literal.
         * For backward compatibility, Node.js only uses JSON string literals within this
         * property when necessary to avoid ambiguity. Third-party code should be prepared
         * to handle both possible entry formats.
         * @since v15.6.0
         */
        readonly subjectAltName: string | undefined;
        /**
         * A textual representation of the certificate's authority information access
         * extension.
         *
         * This is a line feed separated list of access descriptions. Each line begins with
         * the access method and the kind of the access location, followed by a colon and
         * the value associated with the access location.
         *
         * After the prefix denoting the access method and the kind of the access location,
         * the remainder of each line might be enclosed in quotes to indicate that the
         * value is a JSON string literal. For backward compatibility, Node.js only uses
         * JSON string literals within this property when necessary to avoid ambiguity.
         * Third-party code should be prepared to handle both possible entry formats.
         * @since v15.6.0
         */
        readonly infoAccess: string | undefined;
        /**
         * An array detailing the key usages for this certificate.
         * @since v15.6.0
         */
        readonly keyUsage: string[];
        /**
         * The issuer identification included in this certificate.
         * @since v15.6.0
         */
        readonly issuer: string;
        /**
         * The issuer certificate or `undefined` if the issuer certificate is not
         * available.
         * @since v15.9.0
         */
        readonly issuerCertificate?: X509Certificate | undefined;
        /**
         * The public key `KeyObject` for this certificate.
         * @since v15.6.0
         */
        readonly publicKey: KeyObject;
        /**
         * A `Buffer` containing the DER encoding of this certificate.
         * @since v15.6.0
         */
        readonly raw: Buffer;
        /**
         * The serial number of this certificate.
         *
         * Serial numbers are assigned by certificate authorities and do not uniquely
         * identify certificates. Consider using `x509.fingerprint256` as a unique
         * identifier instead.
         * @since v15.6.0
         */
        readonly serialNumber: string;
        /**
         * The date/time from which this certificate is considered valid.
         * @since v15.6.0
         */
        readonly validFrom: string;
        /**
         * The date/time from which this certificate is valid, encapsulated in a `Date` object.
         * @since v22.10.0
         */
        readonly validFromDate: Date;
        /**
         * The date/time until which this certificate is considered valid.
         * @since v15.6.0
         */
        readonly validTo: string;
        /**
         * The date/time until which this certificate is valid, encapsulated in a `Date` object.
         * @since v22.10.0
         */
        readonly validToDate: Date;
        constructor(buffer: BinaryLike);
        /**
         * Checks whether the certificate matches the given email address.
         *
         * If the `'subject'` option is undefined or set to `'default'`, the certificate
         * subject is only considered if the subject alternative name extension either does
         * not exist or does not contain any email addresses.
         *
         * If the `'subject'` option is set to `'always'` and if the subject alternative
         * name extension either does not exist or does not contain a matching email
         * address, the certificate subject is considered.
         *
         * If the `'subject'` option is set to `'never'`, the certificate subject is never
         * considered, even if the certificate contains no subject alternative names.
         * @since v15.6.0
         * @return Returns `email` if the certificate matches, `undefined` if it does not.
         */
        checkEmail(email: string, options?: Pick<X509CheckOptions, "subject">): string | undefined;
        /**
         * Checks whether the certificate matches the given host name.
         *
         * If the certificate matches the given host name, the matching subject name is
         * returned. The returned name might be an exact match (e.g., `foo.example.com`)
         * or it might contain wildcards (e.g., `*.example.com`). Because host name
         * comparisons are case-insensitive, the returned subject name might also differ
         * from the given `name` in capitalization.
         *
         * If the `'subject'` option is undefined or set to `'default'`, the certificate
         * subject is only considered if the subject alternative name extension either does
         * not exist or does not contain any DNS names. This behavior is consistent with [RFC 2818](https://www.rfc-editor.org/rfc/rfc2818.txt) ("HTTP Over TLS").
         *
         * If the `'subject'` option is set to `'always'` and if the subject alternative
         * name extension either does not exist or does not contain a matching DNS name,
         * the certificate subject is considered.
         *
         * If the `'subject'` option is set to `'never'`, the certificate subject is never
         * considered, even if the certificate contains no subject alternative names.
         * @since v15.6.0
         * @return Returns a subject name that matches `name`, or `undefined` if no subject name matches `name`.
         */
        checkHost(name: string, options?: X509CheckOptions): string | undefined;
        /**
         * Checks whether the certificate matches the given IP address (IPv4 or IPv6).
         *
         * Only [RFC 5280](https://www.rfc-editor.org/rfc/rfc5280.txt) `iPAddress` subject alternative names are considered, and they
         * must match the given `ip` address exactly. Other subject alternative names as
         * well as the subject field of the certificate are ignored.
         * @since v15.6.0
         * @return Returns `ip` if the certificate matches, `undefined` if it does not.
         */
        checkIP(ip: string): string | undefined;
        /**
         * Checks whether this certificate was issued by the given `otherCert`.
         * @since v15.6.0
         */
        checkIssued(otherCert: X509Certificate): boolean;
        /**
         * Checks whether the public key for this certificate is consistent with
         * the given private key.
         * @since v15.6.0
         * @param privateKey A private key.
         */
        checkPrivateKey(privateKey: KeyObject): boolean;
        /**
         * There is no standard JSON encoding for X509 certificates. The`toJSON()` method returns a string containing the PEM encoded
         * certificate.
         * @since v15.6.0
         */
        toJSON(): string;
        /**
         * Returns information about this certificate using the legacy `certificate object` encoding.
         * @since v15.6.0
         */
        toLegacyObject(): PeerCertificate;
        /**
         * Returns the PEM-encoded certificate.
         * @since v15.6.0
         */
        toString(): string;
        /**
         * Verifies that this certificate was signed by the given public key.
         * Does not perform any other validation checks on the certificate.
         * @since v15.6.0
         * @param publicKey A public key.
         */
        verify(publicKey: KeyObject): boolean;
    }
    type LargeNumberLike = NodeJS.ArrayBufferView | SharedArrayBuffer | ArrayBuffer | bigint;
    interface GeneratePrimeOptions {
        add?: LargeNumberLike | undefined;
        rem?: LargeNumberLike | undefined;
        /**
         * @default false
         */
        safe?: boolean | undefined;
        bigint?: boolean | undefined;
    }
    interface GeneratePrimeOptionsBigInt extends GeneratePrimeOptions {
        bigint: true;
    }
    interface GeneratePrimeOptionsArrayBuffer extends GeneratePrimeOptions {
        bigint?: false | undefined;
    }
    /**
     * Generates a pseudorandom prime of `size` bits.
     *
     * If `options.safe` is `true`, the prime will be a safe prime -- that is, `(prime - 1) / 2` will also be a prime.
     *
     * The `options.add` and `options.rem` parameters can be used to enforce additional
     * requirements, e.g., for Diffie-Hellman:
     *
     * * If `options.add` and `options.rem` are both set, the prime will satisfy the
     * condition that `prime % add = rem`.
     * * If only `options.add` is set and `options.safe` is not `true`, the prime will
     * satisfy the condition that `prime % add = 1`.
     * * If only `options.add` is set and `options.safe` is set to `true`, the prime
     * will instead satisfy the condition that `prime % add = 3`. This is necessary
     * because `prime % add = 1` for `options.add > 2` would contradict the condition
     * enforced by `options.safe`.
     * * `options.rem` is ignored if `options.add` is not given.
     *
     * Both `options.add` and `options.rem` must be encoded as big-endian sequences
     * if given as an `ArrayBuffer`, `SharedArrayBuffer`, `TypedArray`, `Buffer`, or `DataView`.
     *
     * By default, the prime is encoded as a big-endian sequence of octets
     * in an [ArrayBuffer](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/ArrayBuffer). If the `bigint` option is `true`, then a
     * [bigint](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/BigInt) is provided.
     * @since v15.8.0
     * @param size The size (in bits) of the prime to generate.
     */
    function generatePrime(size: number, callback: (err: Error | null, prime: ArrayBuffer) => void): void;
    function generatePrime(
        size: number,
        options: GeneratePrimeOptionsBigInt,
        callback: (err: Error | null, prime: bigint) => void,
    ): void;
    function generatePrime(
        size: number,
        options: GeneratePrimeOptionsArrayBuffer,
        callback: (err: Error | null, prime: ArrayBuffer) => void,
    ): void;
    function generatePrime(
        size: number,
        options: GeneratePrimeOptions,
        callback: (err: Error | null, prime: ArrayBuffer | bigint) => void,
    ): void;
    /**
     * Generates a pseudorandom prime of `size` bits.
     *
     * If `options.safe` is `true`, the prime will be a safe prime -- that is, `(prime - 1) / 2` will also be a prime.
     *
     * The `options.add` and `options.rem` parameters can be used to enforce additional
     * requirements, e.g., for Diffie-Hellman:
     *
     * * If `options.add` and `options.rem` are both set, the prime will satisfy the
     * condition that `prime % add = rem`.
     * * If only `options.add` is set and `options.safe` is not `true`, the prime will
     * satisfy the condition that `prime % add = 1`.
     * * If only `options.add` is set and `options.safe` is set to `true`, the prime
     * will instead satisfy the condition that `prime % add = 3`. This is necessary
     * because `prime % add = 1` for `options.add > 2` would contradict the condition
     * enforced by `options.safe`.
     * * `options.rem` is ignored if `options.add` is not given.
     *
     * Both `options.add` and `options.rem` must be encoded as big-endian sequences
     * if given as an `ArrayBuffer`, `SharedArrayBuffer`, `TypedArray`, `Buffer`, or `DataView`.
     *
     * By default, the prime is encoded as a big-endian sequence of octets
     * in an [ArrayBuffer](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/ArrayBuffer). If the `bigint` option is `true`, then a
     * [bigint](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/BigInt) is provided.
     * @since v15.8.0
     * @param size The size (in bits) of the prime to generate.
     */
    function generatePrimeSync(size: number): ArrayBuffer;
    function generatePrimeSync(size: number, options: GeneratePrimeOptionsBigInt): bigint;
    function generatePrimeSync(size: number, options: GeneratePrimeOptionsArrayBuffer): ArrayBuffer;
    function generatePrimeSync(size: number, options: GeneratePrimeOptions): ArrayBuffer | bigint;
    interface CheckPrimeOptions {
        /**
         * The number of Miller-Rabin probabilistic primality iterations to perform.
         * When the value is 0 (zero), a number of checks is used that yields a false positive rate of at most `2**-64` for random input.
         * Care must be used when selecting a number of checks.
         * Refer to the OpenSSL documentation for the BN_is_prime_ex function nchecks options for more details.
         *
         * @default 0
         */
        checks?: number | undefined;
    }
    /**
     * Checks the primality of the `candidate`.
     * @since v15.8.0
     * @param candidate A possible prime encoded as a sequence of big endian octets of arbitrary length.
     */
    function checkPrime(value: LargeNumberLike, callback: (err: Error | null, result: boolean) => void): void;
    function checkPrime(
        value: LargeNumberLike,
        options: CheckPrimeOptions,
        callback: (err: Error | null, result: boolean) => void,
    ): void;
    /**
     * Checks the primality of the `candidate`.
     * @since v15.8.0
     * @param candidate A possible prime encoded as a sequence of big endian octets of arbitrary length.
     * @return `true` if the candidate is a prime with an error probability less than `0.25 ** options.checks`.
     */
    function checkPrimeSync(candidate: LargeNumberLike, options?: CheckPrimeOptions): boolean;
    /**
     * Load and set the `engine` for some or all OpenSSL functions (selected by flags).
     *
     * `engine` could be either an id or a path to the engine's shared library.
     *
     * The optional `flags` argument uses `ENGINE_METHOD_ALL` by default. The `flags` is a bit field taking one of or a mix of the following flags (defined in `crypto.constants`):
     *
     * * `crypto.constants.ENGINE_METHOD_RSA`
     * * `crypto.constants.ENGINE_METHOD_DSA`
     * * `crypto.constants.ENGINE_METHOD_DH`
     * * `crypto.constants.ENGINE_METHOD_RAND`
     * * `crypto.constants.ENGINE_METHOD_EC`
     * * `crypto.constants.ENGINE_METHOD_CIPHERS`
     * * `crypto.constants.ENGINE_METHOD_DIGESTS`
     * * `crypto.constants.ENGINE_METHOD_PKEY_METHS`
     * * `crypto.constants.ENGINE_METHOD_PKEY_ASN1_METHS`
     * * `crypto.constants.ENGINE_METHOD_ALL`
     * * `crypto.constants.ENGINE_METHOD_NONE`
     * @since v0.11.11
     * @param flags
     */
    function setEngine(engine: string, flags?: number): void;
    /**
     * A convenient alias for {@link webcrypto.getRandomValues}. This
     * implementation is not compliant with the Web Crypto spec, to write
     * web-compatible code use {@link webcrypto.getRandomValues} instead.
     * @since v17.4.0
     * @return Returns `typedArray`.
     */
    function getRandomValues<T extends webcrypto.BufferSource>(typedArray: T): T;
    /**
     * A convenient alias for `crypto.webcrypto.subtle`.
     * @since v17.4.0
     */
    const subtle: webcrypto.SubtleCrypto;
    /**
     * An implementation of the Web Crypto API standard.
     *
     * See the {@link https://nodejs.org/docs/latest/api/webcrypto.html Web Crypto API documentation} for details.
     * @since v15.0.0
     */
    const webcrypto: webcrypto.Crypto;
    namespace webcrypto {
        type BufferSource = ArrayBufferView | ArrayBuffer;
        type KeyFormat = "jwk" | "pkcs8" | "raw" | "spki";
        type KeyType = "private" | "public" | "secret";
        type KeyUsage =
            | "decrypt"
            | "deriveBits"
            | "deriveKey"
            | "encrypt"
            | "sign"
            | "unwrapKey"
            | "verify"
            | "wrapKey";
        type AlgorithmIdentifier = Algorithm | string;
        type HashAlgorithmIdentifier = AlgorithmIdentifier;
        type NamedCurve = string;
        type BigInteger = Uint8Array;
        interface AesCbcParams extends Algorithm {
            iv: BufferSource;
        }
        interface AesCtrParams extends Algorithm {
            counter: BufferSource;
            length: number;
        }
        interface AesDerivedKeyParams extends Algorithm {
            length: number;
        }
        interface AesGcmParams extends Algorithm {
            additionalData?: BufferSource;
            iv: BufferSource;
            tagLength?: number;
        }
        interface AesKeyAlgorithm extends KeyAlgorithm {
            length: number;
        }
        interface AesKeyGenParams extends Algorithm {
            length: number;
        }
        interface Algorithm {
            name: string;
        }
        interface EcKeyAlgorithm extends KeyAlgorithm {
            namedCurve: NamedCurve;
        }
        interface EcKeyGenParams extends Algorithm {
            namedCurve: NamedCurve;
        }
        interface EcKeyImportParams extends Algorithm {
            namedCurve: NamedCurve;
        }
        interface EcdhKeyDeriveParams extends Algorithm {
            public: CryptoKey;
        }
        interface EcdsaParams extends Algorithm {
            hash: HashAlgorithmIdentifier;
        }
        interface Ed448Params extends Algorithm {
            context?: BufferSource;
        }
        interface HkdfParams extends Algorithm {
            hash: HashAlgorithmIdentifier;
            info: BufferSource;
            salt: BufferSource;
        }
        interface HmacImportParams extends Algorithm {
            hash: HashAlgorithmIdentifier;
            length?: number;
        }
        interface HmacKeyAlgorithm extends KeyAlgorithm {
            hash: KeyAlgorithm;
            length: number;
        }
        interface HmacKeyGenParams extends Algorithm {
            hash: HashAlgorithmIdentifier;
            length?: number;
        }
        interface JsonWebKey {
            alg?: string;
            crv?: string;
            d?: string;
            dp?: string;
            dq?: string;
            e?: string;
            ext?: boolean;
            k?: string;
            key_ops?: string[];
            kty?: string;
            n?: string;
            oth?: RsaOtherPrimesInfo[];
            p?: string;
            q?: string;
            qi?: string;
            use?: string;
            x?: string;
            y?: string;
        }
        interface KeyAlgorithm {
            name: string;
        }
        interface Pbkdf2Params extends Algorithm {
            hash: HashAlgorithmIdentifier;
            iterations: number;
            salt: BufferSource;
        }
        interface RsaHashedImportParams extends Algorithm {
            hash: HashAlgorithmIdentifier;
        }
        interface RsaHashedKeyAlgorithm extends RsaKeyAlgorithm {
            hash: KeyAlgorithm;
        }
        interface RsaHashedKeyGenParams extends RsaKeyGenParams {
            hash: HashAlgorithmIdentifier;
        }
        interface RsaKeyAlgorithm extends KeyAlgorithm {
            modulusLength: number;
            publicExponent: BigInteger;
        }
        interface RsaKeyGenParams extends Algorithm {
            modulusLength: number;
            publicExponent: BigInteger;
        }
        interface RsaOaepParams extends Algorithm {
            label?: BufferSource;
        }
        interface RsaOtherPrimesInfo {
            d?: string;
            r?: string;
            t?: string;
        }
        interface RsaPssParams extends Algorithm {
            saltLength: number;
        }
        /**
         * Importing the `webcrypto` object (`import { webcrypto } from 'node:crypto'`) gives an instance of the `Crypto` class.
         * `Crypto` is a singleton that provides access to the remainder of the crypto API.
         * @since v15.0.0
         */
        interface Crypto {
            /**
             * Provides access to the `SubtleCrypto` API.
             * @since v15.0.0
             */
            readonly subtle: SubtleCrypto;
            /**
             * Generates cryptographically strong random values.
             * The given `typedArray` is filled with random values, and a reference to `typedArray` is returned.
             *
             * The given `typedArray` must be an integer-based instance of {@link NodeJS.TypedArray}, i.e. `Float32Array` and `Float64Array` are not accepted.
             *
             * An error will be thrown if the given `typedArray` is larger than 65,536 bytes.
             * @since v15.0.0
             */
            getRandomValues<T extends Exclude<NodeJS.TypedArray, Float32Array | Float64Array>>(typedArray: T): T;
            /**
             * Generates a random {@link https://www.rfc-editor.org/rfc/rfc4122.txt RFC 4122} version 4 UUID.
             * The UUID is generated using a cryptographic pseudorandom number generator.
             * @since v16.7.0
             */
            randomUUID(): UUID;
            CryptoKey: CryptoKeyConstructor;
        }
        // This constructor throws ILLEGAL_CONSTRUCTOR so it should not be newable.
        interface CryptoKeyConstructor {
            /** Illegal constructor */
            (_: { readonly _: unique symbol }): never; // Allows instanceof to work but not be callable by the user.
            readonly length: 0;
            readonly name: "CryptoKey";
            readonly prototype: CryptoKey;
        }
        /**
         * @since v15.0.0
         */
        interface CryptoKey {
            /**
             * An object detailing the algorithm for which the key can be used along with additional algorithm-specific parameters.
             * @since v15.0.0
             */
            readonly algorithm: KeyAlgorithm;
            /**
             * When `true`, the {@link CryptoKey} can be extracted using either `subtleCrypto.exportKey()` or `subtleCrypto.wrapKey()`.
             * @since v15.0.0
             */
            readonly extractable: boolean;
            /**
             * A string identifying whether the key is a symmetric (`'secret'`) or asymmetric (`'private'` or `'public'`) key.
             * @since v15.0.0
             */
            readonly type: KeyType;
            /**
             * An array of strings identifying the operations for which the key may be used.
             *
             * The possible usages are:
             * - `'encrypt'` - The key may be used to encrypt data.
             * - `'decrypt'` - The key may be used to decrypt data.
             * - `'sign'` - The key may be used to generate digital signatures.
             * - `'verify'` - The key may be used to verify digital signatures.
             * - `'deriveKey'` - The key may be used to derive a new key.
             * - `'deriveBits'` - The key may be used to derive bits.
             * - `'wrapKey'` - The key may be used to wrap another key.
             * - `'unwrapKey'` - The key may be used to unwrap another key.
             *
             * Valid key usages depend on the key algorithm (identified by `cryptokey.algorithm.name`).
             * @since v15.0.0
             */
            readonly usages: KeyUsage[];
        }
        /**
         * The `CryptoKeyPair` is a simple dictionary object with `publicKey` and `privateKey` properties, representing an asymmetric key pair.
         * @since v15.0.0
         */
        interface CryptoKeyPair {
            /**
             * A {@link CryptoKey} whose type will be `'private'`.
             * @since v15.0.0
             */
            privateKey: CryptoKey;
            /**
             * A {@link CryptoKey} whose type will be `'public'`.
             * @since v15.0.0
             */
            publicKey: CryptoKey;
        }
        /**
         * @since v15.0.0
         */
        interface SubtleCrypto {
            /**
             * Using the method and parameters specified in `algorithm` and the keying material provided by `key`,
             * `subtle.decrypt()` attempts to decipher the provided `data`. If successful,
             * the returned promise will be resolved with an `<ArrayBuffer>` containing the plaintext result.
             *
             * The algorithms currently supported include:
             *
             * - `'RSA-OAEP'`
             * - `'AES-CTR'`
             * - `'AES-CBC'`
             * - `'AES-GCM'`
             * @since v15.0.0
             */
            decrypt(
                algorithm: AlgorithmIdentifier | RsaOaepParams | AesCtrParams | AesCbcParams | AesGcmParams,
                key: CryptoKey,
                data: BufferSource,
            ): Promise<ArrayBuffer>;
            /**
             * Using the method and parameters specified in `algorithm` and the keying material provided by `baseKey`,
             * `subtle.deriveBits()` attempts to generate `length` bits.
             * The Node.js implementation requires that when `length` is a number it must be multiple of `8`.
             * When `length` is `null` the maximum number of bits for a given algorithm is generated. This is allowed
             * for the `'ECDH'`, `'X25519'`, and `'X448'` algorithms.
             * If successful, the returned promise will be resolved with an `<ArrayBuffer>` containing the generated data.
             *
             * The algorithms currently supported include:
             *
             * - `'ECDH'`
             * - `'X25519'`
             * - `'X448'`
             * - `'HKDF'`
             * - `'PBKDF2'`
             * @since v15.0.0
             */
            deriveBits(algorithm: EcdhKeyDeriveParams, baseKey: CryptoKey, length: number | null): Promise<ArrayBuffer>;
            deriveBits(
                algorithm: AlgorithmIdentifier | HkdfParams | Pbkdf2Params,
                baseKey: CryptoKey,
                length: number,
            ): Promise<ArrayBuffer>;
            /**
             * Using the method and parameters specified in `algorithm`, and the keying material provided by `baseKey`,
             * `subtle.deriveKey()` attempts to generate a new <CryptoKey>` based on the method and parameters in `derivedKeyAlgorithm`.
             *
             * Calling `subtle.deriveKey()` is equivalent to calling `subtle.deriveBits()` to generate raw keying material,
             * then passing the result into the `subtle.importKey()` method using the `deriveKeyAlgorithm`, `extractable`, and `keyUsages` parameters as input.
             *
             * The algorithms currently supported include:
             *
             * - `'ECDH'`
             * - `'X25519'`
             * - `'X448'`
             * - `'HKDF'`
             * - `'PBKDF2'`
             * @param keyUsages See {@link https://nodejs.org/docs/latest/api/webcrypto.html#cryptokeyusages Key usages}.
             * @since v15.0.0
             */
            deriveKey(
                algorithm: AlgorithmIdentifier | EcdhKeyDeriveParams | HkdfParams | Pbkdf2Params,
                baseKey: CryptoKey,
                derivedKeyAlgorithm:
                    | AlgorithmIdentifier
                    | AesDerivedKeyParams
                    | HmacImportParams
                    | HkdfParams
                    | Pbkdf2Params,
                extractable: boolean,
                keyUsages: readonly KeyUsage[],
            ): Promise<CryptoKey>;
            /**
             * Using the method identified by `algorithm`, `subtle.digest()` attempts to generate a digest of `data`.
             * If successful, the returned promise is resolved with an `<ArrayBuffer>` containing the computed digest.
             *
             * If `algorithm` is provided as a `<string>`, it must be one of:
             *
             * - `'SHA-1'`
             * - `'SHA-256'`
             * - `'SHA-384'`
             * - `'SHA-512'`
             *
             * If `algorithm` is provided as an `<Object>`, it must have a `name` property whose value is one of the above.
             * @since v15.0.0
             */
            digest(algorithm: AlgorithmIdentifier, data: BufferSource): Promise<ArrayBuffer>;
            /**
             * Using the method and parameters specified by `algorithm` and the keying material provided by `key`,
             * `subtle.encrypt()` attempts to encipher `data`. If successful,
             * the returned promise is resolved with an `<ArrayBuffer>` containing the encrypted result.
             *
             * The algorithms currently supported include:
             *
             * - `'RSA-OAEP'`
             * - `'AES-CTR'`
             * - `'AES-CBC'`
             * - `'AES-GCM'`
             * @since v15.0.0
             */
            encrypt(
                algorithm: AlgorithmIdentifier | RsaOaepParams | AesCtrParams | AesCbcParams | AesGcmParams,
                key: CryptoKey,
                data: BufferSource,
            ): Promise<ArrayBuffer>;
            /**
             * Exports the given key into the specified format, if supported.
             *
             * If the `<CryptoKey>` is not extractable, the returned promise will reject.
             *
             * When `format` is either `'pkcs8'` or `'spki'` and the export is successful,
             * the returned promise will be resolved with an `<ArrayBuffer>` containing the exported key data.
             *
             * When `format` is `'jwk'` and the export is successful, the returned promise will be resolved with a
             * JavaScript object conforming to the {@link https://tools.ietf.org/html/rfc7517 JSON Web Key} specification.
             * @param format Must be one of `'raw'`, `'pkcs8'`, `'spki'`, or `'jwk'`.
             * @returns `<Promise>` containing `<ArrayBuffer>`.
             * @since v15.0.0
             */
            exportKey(format: "jwk", key: CryptoKey): Promise<JsonWebKey>;
            exportKey(format: Exclude<KeyFormat, "jwk">, key: CryptoKey): Promise<ArrayBuffer>;
            /**
             * Using the method and parameters provided in `algorithm`,
             * `subtle.generateKey()` attempts to generate new keying material.
             * Depending the method used, the method may generate either a single `<CryptoKey>` or a `<CryptoKeyPair>`.
             *
             * The `<CryptoKeyPair>` (public and private key) generating algorithms supported include:
             *
             * - `'RSASSA-PKCS1-v1_5'`
             * - `'RSA-PSS'`
             * - `'RSA-OAEP'`
             * - `'ECDSA'`
             * - `'Ed25519'`
             * - `'Ed448'`
             * - `'ECDH'`
             * - `'X25519'`
             * - `'X448'`
             * The `<CryptoKey>` (secret key) generating algorithms supported include:
             *
             * - `'HMAC'`
             * - `'AES-CTR'`
             * - `'AES-CBC'`
             * - `'AES-GCM'`
             * - `'AES-KW'`
             * @param keyUsages See {@link https://nodejs.org/docs/latest/api/webcrypto.html#cryptokeyusages Key usages}.
             * @since v15.0.0
             */
            generateKey(
                algorithm: RsaHashedKeyGenParams | EcKeyGenParams,
                extractable: boolean,
                keyUsages: readonly KeyUsage[],
            ): Promise<CryptoKeyPair>;
            generateKey(
                algorithm: AesKeyGenParams | HmacKeyGenParams | Pbkdf2Params,
                extractable: boolean,
                keyUsages: readonly KeyUsage[],
            ): Promise<CryptoKey>;
            generateKey(
                algorithm: AlgorithmIdentifier,
                extractable: boolean,
                keyUsages: KeyUsage[],
            ): Promise<CryptoKeyPair | CryptoKey>;
            /**
             * The `subtle.importKey()` method attempts to interpret the provided `keyData` as the given `format`
             * to create a `<CryptoKey>` instance using the provided `algorithm`, `extractable`, and `keyUsages` arguments.
             * If the import is successful, the returned promise will be resolved with the created `<CryptoKey>`.
             *
             * If importing a `'PBKDF2'` key, `extractable` must be `false`.
             * @param format Must be one of `'raw'`, `'pkcs8'`, `'spki'`, or `'jwk'`.
             * @param keyUsages See {@link https://nodejs.org/docs/latest/api/webcrypto.html#cryptokeyusages Key usages}.
             * @since v15.0.0
             */
            importKey(
                format: "jwk",
                keyData: JsonWebKey,
                algorithm:
                    | AlgorithmIdentifier
                    | RsaHashedImportParams
                    | EcKeyImportParams
                    | HmacImportParams
                    | AesKeyAlgorithm,
                extractable: boolean,
                keyUsages: readonly KeyUsage[],
            ): Promise<CryptoKey>;
            importKey(
                format: Exclude<KeyFormat, "jwk">,
                keyData: BufferSource,
                algorithm:
                    | AlgorithmIdentifier
                    | RsaHashedImportParams
                    | EcKeyImportParams
                    | HmacImportParams
                    | AesKeyAlgorithm,
                extractable: boolean,
                keyUsages: KeyUsage[],
            ): Promise<CryptoKey>;
            /**
             * Using the method and parameters given by `algorithm` and the keying material provided by `key`,
             * `subtle.sign()` attempts to generate a cryptographic signature of `data`. If successful,
             * the returned promise is resolved with an `<ArrayBuffer>` containing the generated signature.
             *
             * The algorithms currently supported include:
             *
             * - `'RSASSA-PKCS1-v1_5'`
             * - `'RSA-PSS'`
             * - `'ECDSA'`
             * - `'Ed25519'`
             * - `'Ed448'`
             * - `'HMAC'`
             * @since v15.0.0
             */
            sign(
                algorithm: AlgorithmIdentifier | RsaPssParams | EcdsaParams | Ed448Params,
                key: CryptoKey,
                data: BufferSource,
            ): Promise<ArrayBuffer>;
            /**
             * In cryptography, "wrapping a key" refers to exporting and then encrypting the keying material.
             * The `subtle.unwrapKey()` method attempts to decrypt a wrapped key and create a `<CryptoKey>` instance.
             * It is equivalent to calling `subtle.decrypt()` first on the encrypted key data (using the `wrappedKey`, `unwrapAlgo`, and `unwrappingKey` arguments as input)
             * then passing the results in to the `subtle.importKey()` method using the `unwrappedKeyAlgo`, `extractable`, and `keyUsages` arguments as inputs.
             * If successful, the returned promise is resolved with a `<CryptoKey>` object.
             *
             * The wrapping algorithms currently supported include:
             *
             * - `'RSA-OAEP'`
             * - `'AES-CTR'`
             * - `'AES-CBC'`
             * - `'AES-GCM'`
             * - `'AES-KW'`
             *
             * The unwrapped key algorithms supported include:
             *
             * - `'RSASSA-PKCS1-v1_5'`
             * - `'RSA-PSS'`
             * - `'RSA-OAEP'`
             * - `'ECDSA'`
             * - `'Ed25519'`
             * - `'Ed448'`
             * - `'ECDH'`
             * - `'X25519'`
             * - `'X448'`
             * - `'HMAC'`
             * - `'AES-CTR'`
             * - `'AES-CBC'`
             * - `'AES-GCM'`
             * - `'AES-KW'`
             * @param format Must be one of `'raw'`, `'pkcs8'`, `'spki'`, or `'jwk'`.
             * @param keyUsages See {@link https://nodejs.org/docs/latest/api/webcrypto.html#cryptokeyusages Key usages}.
             * @since v15.0.0
             */
            unwrapKey(
                format: KeyFormat,
                wrappedKey: BufferSource,
                unwrappingKey: CryptoKey,
                unwrapAlgorithm: AlgorithmIdentifier | RsaOaepParams | AesCtrParams | AesCbcParams | AesGcmParams,
                unwrappedKeyAlgorithm:
                    | AlgorithmIdentifier
                    | RsaHashedImportParams
                    | EcKeyImportParams
                    | HmacImportParams
                    | AesKeyAlgorithm,
                extractable: boolean,
                keyUsages: KeyUsage[],
            ): Promise<CryptoKey>;
            /**
             * Using the method and parameters given in `algorithm` and the keying material provided by `key`,
             * `subtle.verify()` attempts to verify that `signature` is a valid cryptographic signature of `data`.
             * The returned promise is resolved with either `true` or `false`.
             *
             * The algorithms currently supported include:
             *
             * - `'RSASSA-PKCS1-v1_5'`
             * - `'RSA-PSS'`
             * - `'ECDSA'`
             * - `'Ed25519'`
             * - `'Ed448'`
             * - `'HMAC'`
             * @since v15.0.0
             */
            verify(
                algorithm: AlgorithmIdentifier | RsaPssParams | EcdsaParams | Ed448Params,
                key: CryptoKey,
                signature: BufferSource,
                data: BufferSource,
            ): Promise<boolean>;
            /**
             * In cryptography, "wrapping a key" refers to exporting and then encrypting the keying material.
             * The `subtle.wrapKey()` method exports the keying material into the format identified by `format`,
             * then encrypts it using the method and parameters specified by `wrapAlgo` and the keying material provided by `wrappingKey`.
             * It is the equivalent to calling `subtle.exportKey()` using `format` and `key` as the arguments,
             * then passing the result to the `subtle.encrypt()` method using `wrappingKey` and `wrapAlgo` as inputs.
             * If successful, the returned promise will be resolved with an `<ArrayBuffer>` containing the encrypted key data.
             *
             * The wrapping algorithms currently supported include:
             *
             * - `'RSA-OAEP'`
             * - `'AES-CTR'`
             * - `'AES-CBC'`
             * - `'AES-GCM'`
             * - `'AES-KW'`
             * @param format Must be one of `'raw'`, `'pkcs8'`, `'spki'`, or `'jwk'`.
             * @since v15.0.0
             */
            wrapKey(
                format: KeyFormat,
                key: CryptoKey,
                wrappingKey: CryptoKey,
                wrapAlgorithm: AlgorithmIdentifier | RsaOaepParams | AesCtrParams | AesCbcParams | AesGcmParams,
            ): Promise<ArrayBuffer>;
        }
    }

    global {
        var crypto: typeof globalThis extends {
            crypto: infer T;
            onmessage: any;
        } ? T
            : webcrypto.Crypto;
    }
}
declare module "node:crypto" {
    export * from "crypto";
}

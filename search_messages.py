import json
import os
from datetime import datetime

def search_messages(directory, search_term, config_changes=False):
    print(f"Searching for '{search_term}' in directory: {directory}")
    if config_changes:
        print("Also counting config changes for each rig")

    found_messages = []
    config_change_count = {}

    # Alle JSON-Dateien im Verzeichnis verarbeiten
    for filename in sorted(os.listdir(directory)):
        if filename.endswith(".json"):
            filepath = os.path.join(directory, filename)
            print(f"Processing file: {filepath}")

            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    messages = json.load(f)

                    for message in messages:
                        content = message.get('content', '')
                        timestamp_str = message.get('timestamp')

                        if timestamp_str:
                            try:
                                timestamp = datetime.fromisoformat(timestamp_str)

                                # Zähle Konfigurationsänderungen für jedes Rig
                                if config_changes and ("config changed" in content.lower() or "rig config changed" in content.lower()):
                                    # Extrahiere den Rig-Namen aus der Nachricht
                                    rig_name = None
                                    if "mak\_farm1/" in content:
                                        parts = content.split("mak\_farm1/")
                                        if len(parts) > 1:
                                            rig_parts = parts[1].split(":")
                                            if len(rig_parts) > 0:
                                                rig_name = rig_parts[0].strip()

                                    if rig_name:
                                        if rig_name not in config_change_count:
                                            config_change_count[rig_name] = 0
                                        config_change_count[rig_name] += 1

                                # Sammle Nachrichten, die den Suchbegriff enthalten
                                if search_term.lower() in content.lower():
                                    found_messages.append((timestamp, content))
                            except ValueError:
                                pass

            except Exception as e:
                print(f"Error processing {filename}: {e}")

    # Sortiere die gefundenen Nachrichten nach Zeitstempel
    found_messages.sort(key=lambda x: x[0])

    return found_messages, config_change_count

def display_results(found_messages, config_changes=None):
    print(f"\nGefundene Nachrichten: {len(found_messages)}")

    if found_messages:
        print("\nErste 10 Nachrichten:")
        for i, (timestamp, content) in enumerate(found_messages[:10], 1):
            print(f"{i}. {timestamp}: {content}")

        print("\nLetzte 10 Nachrichten:")
        for i, (timestamp, content) in enumerate(found_messages[-10:], len(found_messages)-9):
            print(f"{i}. {timestamp}: {content}")

        # Suche nach Online- und Offline-Ereignissen
        online_events = [(t, c) for t, c in found_messages if "online" in c.lower()]
        offline_events = [(t, c) for t, c in found_messages if "offline" in c.lower()]

        if online_events:
            print(f"\nErstes Online-Event: {online_events[0][0]} - {online_events[0][1]}")
        else:
            print("\nKein Online-Event gefunden.")

        if offline_events:
            print(f"Letztes Offline-Event: {offline_events[-1][0]} - {offline_events[-1][1]}")
        else:
            print("Kein Offline-Event gefunden.")

    # Zeige Konfigurationsänderungen an
    if config_changes:
        print("\n" + "=" * 50)
        print("KONFIGURATIONSÄNDERUNGEN PRO RIG:")
        print("=" * 50)

        total_changes = sum(config_changes.values())
        print(f"Gesamtzahl der Konfigurationsänderungen: {total_changes}")

        # Sortiere nach Anzahl der Änderungen (absteigend)
        sorted_rigs = sorted(config_changes.items(), key=lambda x: x[1], reverse=True)

        for rig, count in sorted_rigs:
            print(f"  {rig}: {count} Änderungen")

if __name__ == "__main__":
    directory_path = "/Users/<USER>/Downloads/Server von mak_miner/hiveos_f1bfc21b-2a31-43de-ae95-bd2f15b547e7"

    print("=" * 50)
    print("Zähle Konfigurationsänderungen für alle Rigs")
    print("=" * 50)

    # Verwende einen leeren Suchbegriff, um alle Nachrichten zu durchsuchen
    found_messages, config_changes = search_messages(directory_path, "", config_changes=True)

    # Zeige die Ergebnisse an
    display_results(found_messages, config_changes)

#!/bin/bash

# antiX ISO-Datei Verifikation
# antiX-23.2 Full (32-bit)

echo "=== antiX ISO-Datei Verifikation ==="
echo ""

ISO_FILE="antiX-23.2_386-full.iso"

if [ ! -f "$ISO_FILE" ]; then
    echo "❌ Fehler: ISO-Datei '$ISO_FILE' nicht gefunden!"
    echo "Warten Sie, bis der Download abgeschlossen ist."
    exit 1
fi

echo "📁 Prüfe antiX ISO-Datei: $ISO_FILE"
echo ""

# Dateigröße prüfen
FILE_SIZE=$(stat -f%z "$ISO_FILE" 2>/dev/null || stat -c%s "$ISO_FILE" 2>/dev/null)
EXPECTED_SIZE=1817574400  # Ungefähr 1.7GB in Bytes

echo "📊 Dateigröße: $(echo $FILE_SIZE | awk '{printf "%.1f MB", $1/1024/1024}')"
echo "📊 Erwartet: $(echo $EXPECTED_SIZE | awk '{printf "%.1f MB", $1/1024/1024}')"

if [ "$FILE_SIZE" -lt 1600000000 ] || [ "$FILE_SIZE" -gt 1900000000 ]; then
    echo "⚠️  Warnung: Dateigröße scheint ungewöhnlich zu sein!"
    echo "Möglicherweise ist der Download noch nicht abgeschlossen."
else
    echo "✅ Dateigröße ist plausibel"
fi

echo ""

# MD5 Checksumme berechnen
echo "🔐 Berechne MD5 Checksumme..."
echo "Dies kann einige Minuten dauern..."

CALCULATED_MD5=$(md5 -q "$ISO_FILE" 2>/dev/null || md5sum "$ISO_FILE" | cut -d' ' -f1)

echo ""
echo "🔑 Berechnete MD5: $CALCULATED_MD5"

# Offizielle MD5 Checksumme (von antiX Website)
OFFICIAL_MD5="861921dda924d5bfb10a9751c3c8a517"

echo "🔑 Offizielle MD5: $OFFICIAL_MD5"

if [ "$CALCULATED_MD5" = "$OFFICIAL_MD5" ]; then
    echo "✅ MD5 Checksumme stimmt überein - ISO ist korrekt!"
else
    echo "❌ MD5 Checksumme stimmt NICHT überein!"
    echo "⚠️  ISO-Datei könnte beschädigt sein oder Download ist unvollständig."
fi

echo ""

# Datei-Typ prüfen
FILE_TYPE=$(file "$ISO_FILE")
echo "📄 Dateityp: $FILE_TYPE"

if echo "$FILE_TYPE" | grep -q "ISO 9660"; then
    echo "✅ Datei ist eine gültige ISO-Datei"
else
    echo "❌ Warnung: Datei scheint keine gültige ISO-Datei zu sein!"
fi

echo ""

# antiX spezifische Prüfungen
if echo "$FILE_TYPE" | grep -q "antiX"; then
    echo "✅ antiX ISO erkannt"
elif echo "$FILE_TYPE" | grep -q "boot"; then
    echo "✅ Bootfähige ISO erkannt"
fi

echo ""
echo "🎯 Verifikation abgeschlossen!"
echo ""

if [ "$CALCULATED_MD5" = "$OFFICIAL_MD5" ]; then
    echo "🚀 ISO ist korrekt! Sie können mit der USB-Erstellung fortfahren:"
    echo "./create_antix_usb.sh"
    echo ""
    echo "🎉 antiX wird auf Ihrem Lenovo S10e deutlich schneller laufen als Ubuntu MATE!"
    echo "   • 3x weniger RAM-Verbrauch"
    echo "   • 3x schnellere Boot-Zeit"
    echo "   • Flüssige Performance auf Intel Atom N270"
else
    echo "⚠️  Bitte warten Sie, bis der Download vollständig abgeschlossen ist,"
    echo "oder laden Sie die ISO-Datei erneut herunter."
fi

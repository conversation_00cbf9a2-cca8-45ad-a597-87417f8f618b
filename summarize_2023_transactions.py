#!/usr/bin/env python3
import csv
from collections import defaultdict
from datetime import datetime

# Function to extract 2023 transactions from the CSV file
def extract_2023_transactions(csv_file_path):
    """
    Extract 2023 transactions from the CSV file.
    
    Args:
        csv_file_path (str): Path to the CSV file
    
    Returns:
        list: List of 2023 transactions
    """
    transactions = []
    
    try:
        with open(csv_file_path, 'r') as file:
            reader = csv.reader(file, delimiter=';')
            header = next(reader)
            
            for row in reader:
                if len(row) >= 10 and row[5] == "Trade" and row[6] and row[8]:
                    # Extract the date in the format DD.MM.YYYY
                    date_str = row[1].split(' ')[0]
                    
                    # Check if the transaction is from 2023
                    if date_str.endswith('2023'):
                        # Extract other relevant information
                        outgoing_asset = row[6]
                        outgoing_amount = float(row[7].replace(',', '.')) if row[7] else 0
                        incoming_asset = row[8]
                        incoming_amount = float(row[9].replace(',', '.')) if row[9] else 0
                        
                        # Store the transaction data
                        transactions.append({
                            'date': date_str,
                            'outgoing_asset': outgoing_asset,
                            'outgoing_amount': outgoing_amount,
                            'incoming_asset': incoming_asset,
                            'incoming_amount': incoming_amount
                        })
        
        return transactions
    
    except Exception as e:
        print(f"Error extracting transactions from {csv_file_path}: {str(e)}")
        return []

# Function to create a summary of 2023 transactions
def create_2023_summary(transactions, output_file):
    """
    Create a summary of 2023 transactions.
    
    Args:
        transactions (list): List of 2023 transactions
        output_file (str): Path to the output file
    """
    # Group transactions by coin
    coin_data = defaultdict(list)
    
    for tx in transactions:
        coin_data[tx['incoming_asset']].append({
            'date': tx['date'],
            'amount': tx['incoming_amount'],
            'paid_with': tx['outgoing_asset'],
            'cost': tx['outgoing_amount']
        })
    
    # Calculate monthly totals
    monthly_data = defaultdict(lambda: defaultdict(float))
    
    for coin, txs in coin_data.items():
        for tx in txs:
            date_obj = datetime.strptime(tx['date'], '%d.%m.%Y')
            month = date_obj.strftime('%m.%Y')
            monthly_data[month][coin] += tx['amount']
    
    # Write the summary to the output file
    with open(output_file, 'w') as file:
        file.write("# Zusammenfassung der Kryptowährungskäufe 2023\n\n")
        file.write("Diese Übersicht zeigt alle Kryptowährungskäufe aus dem Jahr 2023.\n\n")
        
        # Summary by coin
        file.write("## Zusammenfassung nach Coin\n\n")
        file.write("| Coin | Gesamtmenge | Anzahl Käufe |\n")
        file.write("|------|-------------|-------------|\n")
        
        for coin, txs in sorted(coin_data.items()):
            total_amount = sum(tx['amount'] for tx in txs)
            num_purchases = len(txs)
            
            file.write(f"| {coin} | {total_amount:,.2f} | {num_purchases} |\n")
        
        # Monthly summary
        file.write("\n## Monatliche Käufe\n\n")
        
        for month in sorted(monthly_data.keys()):
            month_name = datetime.strptime(month, '%m.%Y').strftime('%B %Y')
            file.write(f"### {month_name}\n\n")
            file.write("| Coin | Gekaufte Menge |\n")
            file.write("|------|---------------|\n")
            
            for coin, amount in sorted(monthly_data[month].items()):
                file.write(f"| {coin} | {amount:,.2f} |\n")
            
            file.write("\n")
        
        # Detailed transactions
        file.write("## Detaillierte Transaktionen\n\n")
        
        for coin, txs in sorted(coin_data.items()):
            file.write(f"### {coin}\n\n")
            file.write("| Datum | Menge | Bezahlt mit | Kosten |\n")
            file.write("|-------|-------|------------|--------|\n")
            
            for tx in sorted(txs, key=lambda x: datetime.strptime(x['date'], '%d.%m.%Y')):
                file.write(f"| {tx['date']} | {tx['amount']:,.2f} | {tx['paid_with']} | {tx['cost']:.8f} |\n")
            
            total_amount = sum(tx['amount'] for tx in txs)
            
            file.write(f"\n**Gesamt {coin}: {total_amount:,.2f}**\n\n")

if __name__ == "__main__":
    # Input and output files
    csv_file_path = '/Users/<USER>/Desktop/MAK-Files/Transactions_2023.csv'
    output_file = 'crypto_purchases_2023_summary.md'
    
    # Extract 2023 transactions from the CSV file
    print("Extracting 2023 transactions from the CSV file...")
    transactions = extract_2023_transactions(csv_file_path)
    print(f"Found {len(transactions)} transactions from 2023")
    
    # Create a summary of 2023 transactions
    print("Creating summary of 2023 transactions...")
    create_2023_summary(transactions, output_file)
    
    print(f"Summary saved to {output_file}")

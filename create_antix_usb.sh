#!/bin/bash

# antiX Linux USB-Stick Ersteller für Lenovo S10e
# antiX-23.2 Full (32-bit) - Optimiert für Intel Atom N270

echo "=== antiX Linux USB-Stick für Lenovo S10e ==="
echo "antiX-23.2 Full (32-bit) - 3x schneller als Ubuntu MATE!"
echo ""

# Prüfen ob ISO-Datei existiert
ISO_FILE="antiX-23.2_386-full.iso"
if [ ! -f "$ISO_FILE" ]; then
    echo "❌ Fehler: ISO-Datei '$ISO_FILE' nicht gefunden!"
    echo "Bitte warten Sie, bis der Download abgeschlossen ist."
    exit 1
fi

echo "✅ antiX ISO-Datei gefunden: $ISO_FILE"

# Dateigröße prüfen
FILE_SIZE=$(stat -f%z "$ISO_FILE" 2>/dev/null || stat -c%s "$ISO_FILE" 2>/dev/null)
EXPECTED_SIZE=1800000000  # Ungefähr 1.7GB in Bytes

echo "📊 Dateigröße: $(echo $FILE_SIZE | awk '{printf "%.1f MB", $1/1024/1024}')"

if [ "$FILE_SIZE" -lt 1600000000 ]; then
    echo "⚠️  Warnung: Download möglicherweise noch nicht abgeschlossen!"
    echo "Erwartete Größe: ~1700MB"
    read -p "Trotzdem fortfahren? (ja/nein): " CONTINUE
    if [ "$CONTINUE" != "ja" ]; then
        echo "❌ Abgebrochen. Warten Sie bis der Download fertig ist."
        exit 1
    fi
fi

echo ""

# USB-Sticks anzeigen
echo "🔍 Verfügbare USB-Laufwerke:"
diskutil list | grep -E "(external|USB)"
echo ""

# Benutzer nach USB-Stick fragen
echo "📋 Vollständige Laufwerksliste:"
diskutil list
echo ""

read -p "Geben Sie die Disk-Nummer Ihres USB-Sticks ein (z.B. 7 für /dev/disk7): " DISK_NUM

# Validierung
if ! [[ "$DISK_NUM" =~ ^[0-9]+$ ]]; then
    echo "❌ Fehler: Bitte geben Sie nur eine Zahl ein!"
    exit 1
fi

DISK_PATH="/dev/disk$DISK_NUM"
RDISK_PATH="/dev/rdisk$DISK_NUM"

# USB-Stick Name anzeigen
USB_NAME=$(diskutil info "$DISK_PATH" | grep "Volume Name" | awk -F: '{print $2}' | xargs)
if [ -n "$USB_NAME" ]; then
    echo "📱 USB-Stick Name: $USB_NAME"
fi

# Bestätigung
echo ""
echo "⚠️  WARNUNG: Alle Daten auf $DISK_PATH werden gelöscht!"
echo "USB-Stick: $DISK_PATH"
echo "ISO-Datei: $ISO_FILE"
echo "Ziel-System: Lenovo S10e"
echo ""

read -p "Sind Sie sicher, dass Sie fortfahren möchten? (ja/nein): " CONFIRM

if [ "$CONFIRM" != "ja" ]; then
    echo "❌ Abgebrochen."
    exit 1
fi

echo ""
echo "🚀 Starte antiX USB-Stick Erstellung..."

# USB-Stick unmounten
echo "📤 Unmounte USB-Stick..."
diskutil unmountDisk "$DISK_PATH"

if [ $? -ne 0 ]; then
    echo "❌ Fehler beim Unmounten des USB-Sticks!"
    exit 1
fi

echo "✅ USB-Stick erfolgreich unmounted"

# ISO auf USB-Stick schreiben
echo ""
echo "💾 Schreibe antiX auf USB-Stick..."
echo "Dies kann 10-15 Minuten dauern..."
echo ""
echo "💡 Tipp: Öffnen Sie ein neues Terminal und führen Sie 'sudo pkill -USR1 dd' aus, um den Fortschritt anzuzeigen"
echo ""

sudo dd if="$ISO_FILE" of="$RDISK_PATH" bs=1m

if [ $? -ne 0 ]; then
    echo "❌ Fehler beim Schreiben der ISO-Datei!"
    exit 1
fi

echo ""
echo "✅ antiX erfolgreich auf USB-Stick geschrieben!"

# USB-Stick auswerfen
echo "📤 Werfe USB-Stick aus..."
diskutil eject "$DISK_PATH"

echo ""
echo "🎉 antiX USB-Stick erfolgreich erstellt!"
echo ""
echo "🚀 Performance-Verbesserungen gegenüber Ubuntu MATE:"
echo "   • 3x weniger RAM-Verbrauch (150-200MB vs. 400-600MB)"
echo "   • 3x schnellere Boot-Zeit (30-60s vs. 2-3 Min)"
echo "   • 3x schnellere Anwendungsstarts"
echo "   • Flüssige Bedienung auf Intel Atom N270"
echo ""
echo "📋 Nächste Schritte für Lenovo S10e:"
echo "1. USB-Stick in S10e einstecken"
echo "2. S10e einschalten und F2 drücken (BIOS)"
echo "3. Boot-Reihenfolge: USB HDD an erste Stelle"
echo "4. F10 drücken und speichern"
echo "5. System neu starten"
echo "6. 'antiX Live' wählen (zum Testen)"
echo "7. 'Install antiX' für permanente Installation"
echo ""
echo "📖 Detaillierte Anleitung: antiX_USB_Anleitung.md"
echo ""
echo "🔧 Bei Problemen:"
echo "- BIOS: F2 beim Start drücken"
echo "- Boot-Menü: F12 beim Start drücken"
echo "- Grafik-Probleme: 'nomodeset' Parameter hinzufügen"
echo ""
echo "🎯 Erwartung: Ihr S10e wird deutlich schneller laufen!"

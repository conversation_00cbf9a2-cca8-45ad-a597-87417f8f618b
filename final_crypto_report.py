#!/usr/bin/env python3
import csv
import sys
import os
from datetime import datetime

# Monthly average prices for ETH in EUR for 2019
ETH_EUR_PRICES = {
    # Format: 'MM.YYYY': price_in_eur
    '01.2019': 108.00,
    '02.2019': 120.00,
    '03.2019': 135.00,
    '04.2019': 150.00,
    '05.2019': 220.00,
    '06.2019': 270.00,
    '07.2019': 240.00,
    '08.2019': 190.00,
    '09.2019': 180.00,
    '10.2019': 175.00,
    '11.2019': 160.00,
    '12.2019': 150.00
}

# Monthly average prices for BNB in EUR for 2019
BNB_EUR_PRICES = {
    # Format: 'MM.YYYY': price_in_eur
    '01.2019': 6.00,
    '02.2019': 8.50,
    '03.2019': 13.00,
    '04.2019': 20.00,
    '05.2019': 28.00,
    '06.2019': 32.00,
    '07.2019': 28.00,
    '08.2019': 25.00,
    '09.2019': 20.00,
    '10.2019': 18.00,
    '11.2019': 16.00,
    '12.2019': 14.00
}

# Function to get ETH price in EUR for a specific date
def get_eth_eur_price(date_str):
    """
    Get the estimated ETH price in EUR for a specific date in 2019.
    
    Args:
        date_str (str): Date string in format 'DD.MM.YYYY'
    
    Returns:
        float: Estimated ETH price in EUR
    """
    parts = date_str.split('.')
    if len(parts) >= 3:
        month_year = f"{parts[1]}.{parts[2]}"
        return ETH_EUR_PRICES.get(month_year, 0)
    return 0

# Function to get BNB price in EUR for a specific date
def get_bnb_eur_price(date_str):
    """
    Get the estimated BNB price in EUR for a specific date in 2019.
    
    Args:
        date_str (str): Date string in format 'DD.MM.YYYY'
    
    Returns:
        float: Estimated BNB price in EUR
    """
    parts = date_str.split('.')
    if len(parts) >= 3:
        month_year = f"{parts[1]}.{parts[2]}"
        return BNB_EUR_PRICES.get(month_year, 0)
    return 0

# Main function to process the transaction data
def process_transactions(csv_file_path):
    """
    Process the transaction data and add EUR prices based on monthly average exchange rates.
    
    Args:
        csv_file_path (str): Path to the CSV file containing transaction data
    
    Returns:
        dict: Dictionary containing processed transaction data
    """
    # Dictionary to store transactions by coin
    coins_data = {}
    
    # Read the transaction data from the CSV file
    with open(csv_file_path, 'r') as file:
        reader = csv.reader(file, delimiter=';')
        header = next(reader)
        
        for row in reader:
            if len(row) >= 10 and row[5] == "Trade" and row[6] and row[8]:
                # Extract the date in the format DD.MM.YYYY
                date_str = row[1].split(' ')[0]
                
                # Extract other relevant information
                outgoing_asset = row[6]
                outgoing_amount = float(row[7].replace(',', '.')) if row[7] else 0
                incoming_asset = row[8]
                incoming_amount = float(row[9].replace(',', '.')) if row[9] else 0
                
                # Calculate price per coin in the original currency
                price_per_coin = outgoing_amount / incoming_amount if incoming_amount > 0 else 0
                
                # Get EUR prices for the payment currency
                if outgoing_asset == 'ETH':
                    payment_eur_price = get_eth_eur_price(date_str)
                elif outgoing_asset == 'BNB':
                    payment_eur_price = get_bnb_eur_price(date_str)
                else:
                    payment_eur_price = 0
                
                # Calculate cost in EUR and price per coin in EUR
                cost_in_eur = outgoing_amount * payment_eur_price
                price_per_coin_eur = cost_in_eur / incoming_amount if incoming_amount > 0 else 0
                
                # Store the transaction data
                if incoming_asset not in coins_data:
                    coins_data[incoming_asset] = []
                
                coins_data[incoming_asset].append({
                    'date': date_str,
                    'amount': incoming_amount,
                    'paid_with': outgoing_asset,
                    'cost': outgoing_amount,
                    'price_per_coin': price_per_coin,
                    'payment_eur_price': payment_eur_price,
                    'cost_in_eur': cost_in_eur,
                    'price_per_coin_eur': price_per_coin_eur
                })
    
    return coins_data

# Function to generate the markdown report
def generate_report(coins_data, output_file):
    """
    Generate a markdown report with the transaction data.
    
    Args:
        coins_data (dict): Dictionary containing processed transaction data
        output_file (str): Path to the output markdown file
    """
    with open(output_file, 'w') as report:
        report.write("# Detailed Cryptocurrency Purchase Report for 2019 with EUR Prices\n\n")
        report.write("This report contains the exact dates and prices of all cryptocurrency purchases made in 2019, including prices in EUR based on monthly average exchange rates.\n\n")
        report.write("## Purchase Details by Coin\n\n")
        
        # Sort coins alphabetically
        for coin in sorted(coins_data.keys()):
            report.write(f"### {coin}\n")
            report.write("| Date | Amount | Paid With | Cost | Price per {0} | EUR Price of Paid With | Cost in EUR | Price per {0} in EUR |\n".format(coin))
            report.write("|------|--------|-----------|------|---------------|-----------------|-------------|---------------------|\n")
            
            total_amount = 0
            total_cost_eur = 0
            
            # Sort transactions by date
            for tx in sorted(coins_data[coin], key=lambda x: datetime.strptime(x['date'], '%d.%m.%Y')):
                total_amount += tx['amount']
                total_cost_eur += tx['cost_in_eur']
                
                report.write("| {0} | {1:,.2f} | {2} | {3:.8f} | {4:.8f} {2} | {5:.2f} € | {6:.2f} € | {7:.8f} € |\n".format(
                    tx['date'],
                    tx['amount'],
                    tx['paid_with'],
                    tx['cost'],
                    tx['price_per_coin'],
                    tx['payment_eur_price'],
                    tx['cost_in_eur'],
                    tx['price_per_coin_eur']
                ))
            
            report.write(f"**Total {coin}: {total_amount:,.2f} | Total Cost: {total_cost_eur:.2f} €**\n\n")
        
        report.write("## Summary\n\n")
        report.write("### Total Purchases by Coin\n\n")
        report.write("| Coin | Total Amount | Total Cost (EUR) |\n")
        report.write("|------|-------------|------------------|\n")
        
        total_spent = 0
        
        for coin in sorted(coins_data.keys()):
            total_amount = sum(tx['amount'] for tx in coins_data[coin])
            total_cost_eur = sum(tx['cost_in_eur'] for tx in coins_data[coin])
            total_spent += total_cost_eur
            
            report.write(f"| {coin} | {total_amount:,.2f} | {total_cost_eur:.2f} € |\n")
        
        report.write(f"\n**Total Spent: {total_spent:.2f} €**\n\n")
        
        report.write("## Monthly Average Prices Used\n\n")
        report.write("### ETH to EUR Monthly Average Prices in 2019\n\n")
        report.write("| Month | Price (EUR) |\n")
        report.write("|-------|-------------|\n")
        
        for month in range(1, 13):
            month_str = f"{month:02d}.2019"
            report.write(f"| {month_str.split('.')[0]} | {ETH_EUR_PRICES[month_str]:.2f} € |\n")
        
        report.write("\n### BNB to EUR Monthly Average Prices in 2019\n\n")
        report.write("| Month | Price (EUR) |\n")
        report.write("|-------|-------------|\n")
        
        for month in range(1, 13):
            month_str = f"{month:02d}.2019"
            report.write(f"| {month_str.split('.')[0]} | {BNB_EUR_PRICES[month_str]:.2f} € |\n")
        
        report.write("\n*Note: EUR prices are based on estimated monthly average prices for ETH and BNB in 2019. These are approximations and may vary slightly from the actual exchange rates at the exact time of each transaction.*\n")

if __name__ == "__main__":
    # Path to the transaction data CSV file
    csv_file_path = '/Users/<USER>/Desktop/MAK-Files/Transactions_2019.csv'
    
    # Output file path
    output_file = 'crypto_purchases_2019_final_report.md'
    
    # Process the transaction data
    print("Processing transaction data...")
    coins_data = process_transactions(csv_file_path)
    
    # Generate the report
    print("Generating report...")
    generate_report(coins_data, output_file)
    
    print(f"Report generated: {output_file}")

#!/usr/bin/env python3
import csv
import sys
import os
import json
import requests
import time
from datetime import datetime, timedelta

# Function to convert date string to Unix timestamp (milliseconds)
def date_to_timestamp(date_str):
    """
    Convert date string to Unix timestamp in milliseconds.

    Args:
        date_str (str): Date string in format 'DD.MM.YYYY HH:MM:SS'

    Returns:
        int: Unix timestamp in milliseconds
    """
    try:
        # Try with time component
        date_obj = datetime.strptime(date_str, '%d.%m.%Y %H:%M:%S')
    except ValueError:
        try:
            # Try without time component, assume start of day
            date_obj = datetime.strptime(date_str, '%d.%m.%Y')
        except ValueError:
            # If still fails, return None
            return None

    # Convert to milliseconds
    return int(date_obj.timestamp() * 1000)

# Function to get historical price from Binance
def get_binance_historical_price(symbol, timestamp):
    """
    Get historical price from Binance for a specific timestamp.

    Args:
        symbol (str): Trading pair symbol (e.g., 'ETHEUR', 'BNBEUR')
        timestamp (int): Unix timestamp in milliseconds

    Returns:
        float: Price at that timestamp or None if not available
    """
    try:
        # Binance klines (candlestick) API endpoint
        url = "https://api.binance.com/api/v3/klines"

        # Parameters - we request 1-hour candles around the timestamp
        params = {
            'symbol': symbol,
            'interval': '1h',
            'startTime': timestamp - 3600000,  # 1 hour before
            'endTime': timestamp + 3600000,    # 1 hour after
            'limit': 10
        }

        # Make the API request
        response = requests.get(url, params=params)
        data = response.json()

        # If we got data, return the closing price of the closest candle
        if data and len(data) > 0:
            # Find the closest candle to the timestamp
            closest_candle = min(data, key=lambda x: abs(int(x[0]) - timestamp))
            # Return the closing price (index 4)
            return float(closest_candle[4])

        return None

    except Exception as e:
        print(f"Error fetching Binance price for {symbol} at {timestamp}: {str(e)}")
        return None

# Function to get historical price from Kraken
def get_kraken_historical_price(pair, timestamp):
    """
    Get historical price from Kraken for a specific timestamp.

    Args:
        pair (str): Trading pair (e.g., 'ETHEUR', 'XBTEUR')
        timestamp (int): Unix timestamp in seconds

    Returns:
        float: Price at that timestamp or None if not available
    """
    try:
        # Convert milliseconds to seconds for Kraken API
        timestamp_seconds = timestamp // 1000

        # Kraken OHLC API endpoint
        url = "https://api.kraken.com/0/public/OHLC"

        # Parameters - we request 1-hour candles around the timestamp
        params = {
            'pair': pair,
            'interval': 60,  # 1 hour
            'since': timestamp_seconds - 3600  # 1 hour before
        }

        # Make the API request
        response = requests.get(url, params=params)
        data = response.json()

        # Check if the request was successful
        if 'result' in data and data['result'] and pair in data['result']:
            ohlc_data = data['result'][pair]

            # Find the closest candle to the timestamp
            closest_candle = min(ohlc_data, key=lambda x: abs(int(x[0]) - timestamp_seconds))

            # Return the closing price (index 4)
            return float(closest_candle[4])

        return None

    except Exception as e:
        print(f"Error fetching Kraken price for {pair} at {timestamp_seconds}: {str(e)}")
        return None

# Function to get the best available price for a cryptocurrency
def get_best_price(crypto, date_str, time_str="00:00:00"):
    """
    Get the best available price from Binance or Kraken.

    Args:
        crypto (str): Cryptocurrency symbol (e.g., 'ETH', 'BNB')
        date_str (str): Date string in format 'DD.MM.YYYY'
        time_str (str): Time string in format 'HH:MM:SS', default is '00:00:00'

    Returns:
        tuple: (price, source) or (None, None) if not available
    """
    # Combine date and time
    datetime_str = f"{date_str} {time_str}"
    timestamp = date_to_timestamp(datetime_str)

    if timestamp is None:
        return None, None

    # Try Binance first
    binance_symbol = f"{crypto}EUR"
    binance_price = get_binance_historical_price(binance_symbol, timestamp)

    if binance_price is not None:
        return binance_price, "Binance"

    # If Binance fails, try Kraken
    # Map common symbols to Kraken format
    kraken_symbol_map = {
        'BTC': 'XXBTZEUR',
        'ETH': 'XETHZEUR',
        'XRP': 'XXRPZEUR',
        'LTC': 'XLTCZEUR',
        'BNB': 'BNBEUR',
        'ADA': 'ADAEUR',
        'DOT': 'DOTEUR',
        'LINK': 'LINKEUR',
        'XLM': 'XXLMZEUR',
        'DOGE': 'XDGEUR',
        'UNI': 'UNIEUR',
        'SOL': 'SOLEUR',
        'MATIC': 'MATICEUR'
    }

    kraken_symbol = kraken_symbol_map.get(crypto, f"{crypto}EUR")
    kraken_price = get_kraken_historical_price(kraken_symbol, timestamp)

    if kraken_price is not None:
        return kraken_price, "Kraken"

    # If both fail, return None
    return None, None

# Function to process transaction data and add exact prices
def process_transactions_with_exact_prices(csv_file_path, output_file):
    """
    Process transaction data and add exact prices from Binance and Kraken.

    Args:
        csv_file_path (str): Path to the CSV file containing transaction data
        output_file (str): Path to the output CSV file
    """
    # Read the transaction data
    transactions = []

    with open(csv_file_path, 'r') as file:
        reader = csv.reader(file, delimiter=';')
        header = next(reader)

        for row in reader:
            if len(row) >= 10 and row[5] == "Trade" and row[6] and row[8]:
                # Extract date and time
                datetime_parts = row[1].split(' ')
                date_str = datetime_parts[0]
                time_str = datetime_parts[1] if len(datetime_parts) > 1 else "00:00:00"

                # Extract other relevant information
                outgoing_asset = row[6]
                outgoing_amount = float(row[7].replace(',', '.')) if row[7] else 0
                incoming_asset = row[8]
                incoming_amount = float(row[9].replace(',', '.')) if row[9] else 0

                # Get exact prices for the payment currency
                if outgoing_asset in ['ETH', 'BNB']:
                    exact_price, source = get_best_price(outgoing_asset, date_str, time_str)

                    # Store the transaction data
                    transactions.append({
                        'date': date_str,
                        'time': time_str,
                        'outgoing_asset': outgoing_asset,
                        'outgoing_amount': outgoing_amount,
                        'incoming_asset': incoming_asset,
                        'incoming_amount': incoming_amount,
                        'exact_price_eur': exact_price,
                        'price_source': source
                    })

                    # Sleep to avoid hitting API rate limits
                    time.sleep(1)

    # Write the results to a CSV file
    with open(output_file, 'w', newline='') as file:
        fieldnames = ['date', 'time', 'outgoing_asset', 'outgoing_amount', 'incoming_asset',
                     'incoming_amount', 'exact_price_eur', 'price_source']
        writer = csv.DictWriter(file, fieldnames=fieldnames)

        writer.writeheader()
        for tx in transactions:
            writer.writerow(tx)

    print(f"Processed {len(transactions)} transactions and saved to {output_file}")
    return transactions

# Function to update the markdown report with exact prices
def update_report_with_exact_prices(transactions, report_file, updated_report_file):
    """
    Update the markdown report with exact prices.

    Args:
        transactions (list): List of transaction dictionaries with exact prices
        report_file (str): Path to the original markdown report
        updated_report_file (str): Path to the updated markdown report
    """
    # Create a lookup dictionary for exact prices
    price_lookup = {}
    for tx in transactions:
        if tx['exact_price_eur'] is not None:
            key = f"{tx['date']}_{tx['outgoing_asset']}_{tx['incoming_asset']}"
            price_lookup[key] = {
                'exact_price_eur': tx['exact_price_eur'],
                'source': tx['price_source']
            }

    # Read the original report
    with open(report_file, 'r') as file:
        report_lines = file.readlines()

    # Process the report line by line
    updated_lines = []
    in_table = False
    current_date = ""
    current_outgoing = ""
    current_incoming = ""

    for line in report_lines:
        # Check if we're in a transaction table
        if '| Date | Amount | Paid With | Cost | Price per' in line:
            in_table = True
            updated_lines.append(line)
            continue

        # Check if we're at the end of a table
        if in_table and line.startswith('**Total'):
            in_table = False
            updated_lines.append(line)
            continue

        # Process table rows
        if in_table and '|' in line and not line.startswith('|---'):
            # Extract date, outgoing and incoming assets
            parts = line.split('|')
            if len(parts) >= 6:
                date_part = parts[1].strip()
                incoming_part = parts[2].strip().split(' ')[0]
                outgoing_part = parts[3].strip()

                # Extract date without time
                date_only = date_part.split(' ')[0]

                # Update current values
                current_date = date_only
                current_outgoing = outgoing_part
                current_incoming = incoming_part

                # Look up exact price
                key = f"{current_date}_{current_outgoing}_{current_incoming}"
                if key in price_lookup:
                    exact_price = price_lookup[key]['exact_price_eur']
                    source = price_lookup[key]['source']

                    # Add a note about the exact price
                    line = line.rstrip() + f" (Exact: {exact_price:.2f} € from {source})\n"

        updated_lines.append(line)

    # Write the updated report
    with open(updated_report_file, 'w') as file:
        file.writelines(updated_lines)

    print(f"Updated report saved to {updated_report_file}")

if __name__ == "__main__":
    # Process each transaction file
    transaction_files = [
        '/Users/<USER>/Desktop/MAK-Files/Transactions_2019.csv',
        '/Users/<USER>/Desktop/MAK-Files/Transactions_2020.csv',
        '/Users/<USER>/Desktop/MAK-Files/Transactions_2021.csv',
        '/Users/<USER>/Desktop/MAK-Files/Transactions_2022.csv',
        '/Users/<USER>/Desktop/MAK-Files/Transactions_2023.csv'
    ]

    # Process a sample of transactions to test the APIs
    print("Fetching exact prices for a sample of transactions...")
    sample_transactions = process_transactions_with_exact_prices(
        transaction_files[0],  # Start with 2019 transactions
        'exact_prices_sample.csv'
    )

    # Update the report with exact prices
    print("Updating report with exact prices...")
    update_report_with_exact_prices(
        sample_transactions,
        'crypto_purchases_2019_2023_report.md',
        'crypto_purchases_2019_2023_with_exact_prices.md'
    )

    print("Done!")

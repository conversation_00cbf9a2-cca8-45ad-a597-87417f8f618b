#!/bin/bash

# Bootfähiger USB-Stick Ersteller für Lenovo S10e
# Ubuntu MATE 18.04.5 LTS (32-bit)

echo "=== Bootfähiger USB-Stick für Lenovo S10e ==="
echo "Ubuntu MATE 18.04.5 LTS (32-bit)"
echo ""

# Prüfen ob ISO-Datei existiert
ISO_FILE="ubuntu-mate-18.04.5-desktop-i386.iso"
if [ ! -f "$ISO_FILE" ]; then
    echo "❌ Fehler: ISO-Datei '$ISO_FILE' nicht gefunden!"
    echo "Bitte stellen Sie sicher, dass die ISO-Datei im aktuellen Verzeichnis liegt."
    exit 1
fi

echo "✅ ISO-Datei gefunden: $ISO_FILE"
echo ""

# USB-Sticks anzeigen
echo "🔍 Verfügbare Laufwerke:"
diskutil list | grep -E "(external|USB)"
echo ""

# Benutzer nach USB-Stick fragen
echo "📋 Vollständige Laufwerksliste:"
diskutil list
echo ""

read -p "Geben Sie die Disk-Nummer Ihres 'Ford' USB-Sticks ein (z.B. 2 für /dev/disk2): " DISK_NUM

# Validierung
if ! [[ "$DISK_NUM" =~ ^[0-9]+$ ]]; then
    echo "❌ Fehler: Bitte geben Sie nur eine Zahl ein!"
    exit 1
fi

DISK_PATH="/dev/disk$DISK_NUM"
RDISK_PATH="/dev/rdisk$DISK_NUM"

# Bestätigung
echo ""
echo "⚠️  WARNUNG: Alle Daten auf $DISK_PATH werden gelöscht!"
echo "USB-Stick: $DISK_PATH"
echo "ISO-Datei: $ISO_FILE"
echo ""

read -p "Sind Sie sicher, dass Sie fortfahren möchten? (ja/nein): " CONFIRM

if [ "$CONFIRM" != "ja" ]; then
    echo "❌ Abgebrochen."
    exit 1
fi

echo ""
echo "🚀 Starte USB-Stick Erstellung..."

# USB-Stick unmounten
echo "📤 Unmounte USB-Stick..."
diskutil unmountDisk "$DISK_PATH"

if [ $? -ne 0 ]; then
    echo "❌ Fehler beim Unmounten des USB-Sticks!"
    exit 1
fi

echo "✅ USB-Stick erfolgreich unmounted"

# ISO auf USB-Stick schreiben
echo ""
echo "💾 Schreibe ISO auf USB-Stick..."
echo "Dies kann 10-20 Minuten dauern..."
echo ""
echo "💡 Tipp: Öffnen Sie ein neues Terminal und führen Sie 'sudo pkill -USR1 dd' aus, um den Fortschritt anzuzeigen"
echo ""

sudo dd if="$ISO_FILE" of="$RDISK_PATH" bs=1m

if [ $? -ne 0 ]; then
    echo "❌ Fehler beim Schreiben der ISO-Datei!"
    exit 1
fi

echo ""
echo "✅ ISO erfolgreich auf USB-Stick geschrieben!"

# USB-Stick auswerfen
echo "📤 Werfe USB-Stick aus..."
diskutil eject "$DISK_PATH"

echo ""
echo "🎉 Bootfähiger USB-Stick erfolgreich erstellt!"
echo ""
echo "📋 Nächste Schritte:"
echo "1. USB-Stick in Lenovo S10e einstecken"
echo "2. S10e einschalten und F2 drücken für BIOS"
echo "3. Boot-Reihenfolge ändern: USB HDD an erste Stelle"
echo "4. F10 drücken und speichern"
echo "5. System neu starten"
echo ""
echo "📖 Detaillierte Anleitung: USB_Boot_Anleitung_Lenovo_S10e.md"
echo ""
echo "🔧 Bei Problemen:"
echo "- BIOS: F2 beim Start drücken"
echo "- Boot-Menü: F12 beim Start drücken"
echo "- Grafik-Probleme: 'nomodeset' Parameter hinzufügen"

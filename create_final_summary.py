#!/usr/bin/env python3
import re
import pandas as pd
from collections import defaultdict

def extract_coin_data_from_report(report_file):
    """
    Extracts coin purchase data from the report file.
    
    Args:
        report_file (str): Path to the report file
    
    Returns:
        dict: Dictionary with coin data by year
    """
    # Dictionary to store coin data by year
    coin_data = defaultdict(lambda: defaultdict(lambda: {'amount': 0, 'cost_eur': 0}))
    
    # Read the report file
    with open(report_file, 'r') as file:
        content = file.read()
    
    # Extract the summary sections for each year
    year_pattern = r'### Total Spent by Year.*?Grand Total \(2019-2023\): ([\d,.]+) €'
    year_match = re.search(year_pattern, content, re.DOTALL)
    
    if year_match:
        year_section = year_match.group(0)
        
        # Extract year data
        year_data_pattern = r'\| (\d{4}) \| ([\d,.]+) € \|'
        year_matches = re.findall(year_data_pattern, year_section)
        
        years = [year for year, _ in year_matches]
    else:
        years = ['2019', '2020', '2021', '2022', '2023']
    
    # Extract the coin summary section
    coin_pattern = r'### Total Purchases by Coin.*?Grand Total \(2019-2023\): ([\d,.]+) €'
    coin_match = re.search(coin_pattern, content, re.DOTALL)
    
    if coin_match:
        coin_section = coin_match.group(0)
        
        # Extract coin data
        coin_data_pattern = r'\| ([A-Z]+) \| ([\d,.]+) \| ([\d,.]+) € \|'
        coin_matches = re.findall(coin_data_pattern, coin_section)
        
        # Store total amounts for each coin
        total_coin_data = {}
        for coin, amount, cost in coin_matches:
            total_coin_data[coin] = {
                'total_amount': float(amount.replace(',', '')),
                'total_cost_eur': float(cost.replace(',', ''))
            }
    else:
        total_coin_data = {}
    
    # Extract detailed purchase data for each year
    for year in years:
        year_pattern = rf'### {year}.*?#### Summary for {year}.*?Total Spent in {year}: ([\d,.]+) €'
        year_match = re.search(year_pattern, content, re.DOTALL)
        
        if year_match:
            year_section = year_match.group(0)
            
            # Extract coin data for this year
            coin_data_pattern = r'\| ([A-Z]+) \| ([\d,.]+) \| ([\d,.]+) € \|'
            coin_matches = re.findall(coin_data_pattern, year_section)
            
            for coin, amount, cost in coin_matches:
                coin_data[coin][year] = {
                    'amount': float(amount.replace(',', '')),
                    'cost_eur': float(cost.replace(',', ''))
                }
    
    return coin_data, total_coin_data

def create_final_summary(coin_data, total_coin_data, output_file):
    """
    Creates a final summary of coin purchases by year.
    
    Args:
        coin_data (dict): Dictionary with coin data by year
        total_coin_data (dict): Dictionary with total coin data
        output_file (str): Path to the output file
    """
    years = ['2019', '2020', '2021', '2022', '2023']
    
    with open(output_file, 'w') as file:
        file.write("# Zusammenfassung der Coin-Käufe über die Jahre\n\n")
        file.write("Diese Übersicht zeigt, wie viel von jedem Coin Sie in den Jahren 2019 bis 2023 gekauft haben.\n\n")
        
        # Table of Contents
        file.write("## Inhaltsverzeichnis\n\n")
        file.write("1. [Gesamtübersicht nach Coin](#gesamtübersicht-nach-coin)\n")
        file.write("2. [Jährliche Investitionen](#jährliche-investitionen)\n")
        file.write("3. [Käufe nach Coin und Jahr](#käufe-nach-coin-und-jahr)\n\n")
        
        # Overall summary by coin
        file.write("## Gesamtübersicht nach Coin\n\n")
        file.write("| Coin | Gesamtmenge | Gesamtkosten (EUR) | Durchschnittspreis pro Coin (EUR) |\n")
        file.write("|------|-------------|---------------------|----------------------------------|\n")
        
        # Sort coins by total cost (highest first)
        sorted_coins = sorted(total_coin_data.keys(), 
                             key=lambda x: total_coin_data[x]['total_cost_eur'] if x in total_coin_data and total_coin_data[x]['total_cost_eur'] > 0 else 0, 
                             reverse=True)
        
        for coin in sorted_coins:
            if coin in total_coin_data:
                total_amount = total_coin_data[coin]['total_amount']
                total_cost = total_coin_data[coin]['total_cost_eur']
                
                if total_amount > 0 and total_cost > 0:
                    avg_price = total_cost / total_amount
                    file.write(f"| {coin} | {total_amount:,.2f} | {total_cost:,.2f} € | {avg_price:.8f} € |\n")
                elif total_amount > 0:
                    file.write(f"| {coin} | {total_amount:,.2f} | {total_cost:,.2f} € | - |\n")
        
        # Yearly investments
        file.write("\n## Jährliche Investitionen\n\n")
        file.write("| Jahr | Gesamtinvestition (EUR) |\n")
        file.write("|------|-------------------------|\n")
        
        yearly_totals = defaultdict(float)
        
        for coin in coin_data:
            for year in coin_data[coin]:
                yearly_totals[year] += coin_data[coin][year]['cost_eur']
        
        total_investment = 0
        for year in years:
            if year in yearly_totals:
                file.write(f"| {year} | {yearly_totals[year]:,.2f} € |\n")
                total_investment += yearly_totals[year]
        
        file.write(f"\n**Gesamtinvestition 2019-2023: {total_investment:,.2f} €**\n")
        
        # Purchases by coin and year
        file.write("\n## Käufe nach Coin und Jahr\n\n")
        
        # Sort coins alphabetically for the detailed view
        for coin in sorted(coin_data.keys()):
            file.write(f"### {coin}\n\n")
            file.write("| Jahr | Gekaufte Menge | Kosten (EUR) | Durchschnittspreis (EUR) |\n")
            file.write("|------|---------------|--------------|-------------------------|\n")
            
            total_amount = 0
            total_cost = 0
            
            for year in years:
                if year in coin_data[coin]:
                    amount = coin_data[coin][year]['amount']
                    cost = coin_data[coin][year]['cost_eur']
                    
                    total_amount += amount
                    total_cost += cost
                    
                    if amount > 0 and cost > 0:
                        avg_price = cost / amount
                        file.write(f"| {year} | {amount:,.2f} | {cost:,.2f} € | {avg_price:.8f} € |\n")
                    elif amount > 0:
                        file.write(f"| {year} | {amount:,.2f} | {cost:,.2f} € | - |\n")
            
            if total_amount > 0 and total_cost > 0:
                overall_avg = total_cost / total_amount
                file.write(f"\n**Gesamt: {total_amount:,.2f} {coin} | Gesamtkosten: {total_cost:,.2f} € | Durchschnittspreis: {overall_avg:.8f} €**\n\n")
            elif total_amount > 0:
                file.write(f"\n**Gesamt: {total_amount:,.2f} {coin} | Gesamtkosten: {total_cost:,.2f} €**\n\n")

if __name__ == "__main__":
    # Input and output files
    report_file = "crypto_purchases_2019_2023_with_exact_prices.md"
    output_file = "coin_purchases_final_summary.md"
    
    # Extract coin data from the report
    coin_data, total_coin_data = extract_coin_data_from_report(report_file)
    
    # Create the final summary
    create_final_summary(coin_data, total_coin_data, output_file)
    
    print(f"Finale Zusammenfassung erstellt und in {output_file} gespeichert.")

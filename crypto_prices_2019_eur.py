#!/usr/bin/env python3
"""
This script contains estimated monthly average prices for ETH and BNB in EUR for 2019.
These are approximations based on historical data.
"""

# Estimated monthly average prices for ETH in EUR for 2019
ETH_EUR_PRICES = {
    # Format: 'MM.YYYY': price_in_eur
    '01.2019': 108.00,
    '02.2019': 120.00,
    '03.2019': 135.00,
    '04.2019': 150.00,
    '05.2019': 220.00,
    '06.2019': 270.00,
    '07.2019': 240.00,
    '08.2019': 190.00,
    '09.2019': 180.00,
    '10.2019': 175.00,
    '11.2019': 160.00,
    '12.2019': 150.00
}

# Estimated monthly average prices for BNB in EUR for 2019
BNB_EUR_PRICES = {
    # Format: 'MM.YYYY': price_in_eur
    '01.2019': 6.00,
    '02.2019': 8.50,
    '03.2019': 13.00,
    '04.2019': 20.00,
    '05.2019': 28.00,
    '06.2019': 32.00,
    '07.2019': 28.00,
    '08.2019': 25.00,
    '09.2019': 20.00,
    '10.2019': 18.00,
    '11.2019': 16.00,
    '12.2019': 14.00
}

# Function to get ETH price in EUR for a specific date
def get_eth_eur_price(date_str):
    """
    Get the estimated ETH price in EUR for a specific date in 2019.
    
    Args:
        date_str (str): Date string in format 'DD.MM.YYYY'
    
    Returns:
        float: Estimated ETH price in EUR
    """
    parts = date_str.split('.')
    if len(parts) >= 3:
        month_year = f"{parts[1]}.{parts[2]}"
        return ETH_EUR_PRICES.get(month_year, 0)
    return 0

# Function to get BNB price in EUR for a specific date
def get_bnb_eur_price(date_str):
    """
    Get the estimated BNB price in EUR for a specific date in 2019.
    
    Args:
        date_str (str): Date string in format 'DD.MM.YYYY'
    
    Returns:
        float: Estimated BNB price in EUR
    """
    parts = date_str.split('.')
    if len(parts) >= 3:
        month_year = f"{parts[1]}.{parts[2]}"
        return BNB_EUR_PRICES.get(month_year, 0)
    return 0

if __name__ == "__main__":
    # Example usage
    print("ETH price on 06.02.2019:", get_eth_eur_price("06.02.2019"), "EUR")
    print("BNB price on 06.02.2019:", get_bnb_eur_price("06.02.2019"), "EUR")

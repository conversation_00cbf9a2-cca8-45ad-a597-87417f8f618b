# Bootfähiger USB-Stick für Lenovo S10e - Ubuntu MATE 18.04.5 LTS

## Übersicht
- **Ziel-System**: Lenovo S10e Netbook
- **Prozessor**: Intel Atom N270 (32-bit)
- **RAM**: 1GB
- **Betriebssystem**: Ubuntu MATE 18.04.5 LTS (32-bit)
- **USB-Stick**: "Ford"

## Voraussetzungen
- USB-Stick mit mindestens 4GB Speicherplatz (Ihr "Ford" USB-Stick)
- macOS Computer für die Erstellung
- Heruntergeladene ISO-Datei: `ubuntu-mate-18.04.5-desktop-i386.iso`

## Schritt 1: USB-Stick vorbereiten

### Terminal öffnen und USB-Stick identifizieren:
```bash
diskutil list
```

Suchen Sie nach Ihrem "Ford" USB-Stick in der Liste. Er wird als `/dev/diskX` angezeigt (X ist eine Nummer).

### USB-Stick unmounten (NICHT auswerfen):
```bash
diskutil unmountDisk /dev/diskX
```
*Ersetzen Sie X durch die richtige Nummer Ihres USB-Sticks*

## Schritt 2: ISO auf USB-Stick schreiben

### Mit dd-Kommando (empfohlen):
```bash
sudo dd if=ubuntu-mate-18.04.5-desktop-i386.iso of=/dev/rdiskX bs=1m
```

**Wichtige Hinweise:**
- Verwenden Sie `/dev/rdiskX` (mit 'r') für bessere Performance
- Ersetzen Sie X durch die richtige Disk-Nummer
- Der Vorgang dauert 10-20 Minuten
- Es gibt keine Fortschrittsanzeige

### Fortschritt anzeigen (optional):
In einem zweiten Terminal-Fenster:
```bash
sudo pkill -USR1 dd
```

## Schritt 3: USB-Stick sicher auswerfen
```bash
diskutil eject /dev/diskX
```

## Schritt 4: Lenovo S10e für USB-Boot konfigurieren

### BIOS-Einstellungen:
1. **S10e einschalten** und sofort **F2** drücken (mehrmals)
2. **Boot-Tab** aufrufen
3. **USB HDD** an erste Stelle setzen
4. **F10** drücken und Änderungen speichern

### Alternative Boot-Methode:
- Beim Start **F12** drücken für Boot-Menü
- USB-Stick auswählen

## Schritt 5: Ubuntu MATE Installation

### Live-System starten:
1. USB-Stick in S10e einstecken
2. System neu starten
3. "Try Ubuntu MATE" wählen (zum Testen)
4. "Install Ubuntu MATE" für permanente Installation

### Empfohlene Einstellungen für S10e:
- **Sprache**: Deutsch
- **Tastatur**: Deutsch
- **Installation**: "Etwas anderes" für manuelle Partitionierung
- **Swap**: 1-2GB (bei 1GB RAM wichtig!)
- **Root (/)**: Rest des Speichers (ext4)

### Performance-Optimierungen nach Installation:
```bash
# Swap-Nutzung reduzieren
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf

# Preload installieren für schnellere Startzeiten
sudo apt update
sudo apt install preload

# Unnötige Dienste deaktivieren
sudo systemctl disable bluetooth
sudo systemctl disable cups-browsed
```

## Schritt 6: Spezielle Anpassungen für Netbooks

### Display-Auflösung optimieren:
```bash
# Für 1024x600 Bildschirm
xrandr --output LVDS1 --mode 1024x600
```

### Energieverwaltung:
```bash
# TLP für bessere Akkulaufzeit installieren
sudo apt install tlp tlp-rdw
sudo systemctl enable tlp
```

### Leichtgewichtige Alternativen installieren:
```bash
# Leichter Browser
sudo apt install midori

# Leichter Texteditor
sudo apt install leafpad

# Leichter Dateimanager (falls Caja zu langsam ist)
sudo apt install pcmanfm
```

## Troubleshooting

### Problem: System startet nicht vom USB
- **Lösung**: BIOS-Einstellungen prüfen, USB-Stick neu erstellen

### Problem: Grafik-Probleme
- **Lösung**: Boot-Parameter hinzufügen: `nomodeset`
- Beim GRUB-Menü 'e' drücken und `nomodeset` zur Kernel-Zeile hinzufügen

### Problem: WLAN funktioniert nicht
- **Lösung**: Zusätzliche Treiber installieren:
```bash
sudo apt install linux-firmware-nonfree
```

### Problem: System zu langsam
- **Lösung**: 
  - Swap-Datei vergrößern
  - Visuelle Effekte deaktivieren
  - Leichtere Anwendungen verwenden

## Wichtige Hinweise für das S10e

1. **RAM-Limitation**: Mit nur 1GB RAM sollten Sie:
   - Nicht zu viele Programme gleichzeitig öffnen
   - Browser-Tabs begrenzen
   - Swap-Speicher aktiviert lassen

2. **Prozessor**: Der Atom N270 ist langsam, daher:
   - Geduld bei Programmstarts
   - Leichtgewichtige Alternativen bevorzugen

3. **Bildschirm**: 1024x600 ist klein, daher:
   - Schriftgröße anpassen
   - Panel auto-hide aktivieren
   - Kompakte Ansichten verwenden

## Support und Updates

Ubuntu MATE 18.04.5 LTS erhielt Updates bis April 2023. Für Sicherheitsupdates können Sie auf ein leichteres, aktuelleres System wie:
- **antiX Linux** (sehr leicht)
- **Puppy Linux** (extrem leicht)
- **Lubuntu 20.04** (falls verfügbar)

wechseln.

---

**Erstellt für**: Lenovo S10e mit USB-Stick "Ford"  
**Datum**: $(date)  
**Ubuntu MATE Version**: 18.04.5 LTS (32-bit)

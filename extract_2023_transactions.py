#!/usr/bin/env python3
import re
import csv
import requests
import time
from datetime import datetime
from collections import defaultdict

# CoinMarketCap API key
CMC_API_KEY = "6fc228a5-5d1c-4c58-a9a0-c8f9bbaf1945"

# Base URL for CoinMarketCap API
CMC_BASE_URL = "https://pro-api.coinmarketcap.com/v1"

# Headers for API requests
headers = {
    'X-CMC_PRO_API_KEY': CMC_API_KEY,
    'Accept': 'application/json'
}

# Function to extract 2023 transactions from the CSV file
def extract_2023_transactions_from_csv(csv_file_path):
    """
    Extract 2023 transactions from the CSV file.

    Args:
        csv_file_path (str): Path to the CSV file

    Returns:
        list: List of 2023 transactions
    """
    transactions = []

    try:
        with open(csv_file_path, 'r') as file:
            print(f"Successfully opened {csv_file_path}")
            reader = csv.reader(file, delimiter=';')
            header = next(reader)
            print(f"CSV header: {header}")

            row_count = 0
            for row in reader:
                row_count += 1
                if len(row) >= 10 and row[5] == "Trade" and row[6] and row[8]:
                    # Extract the date in the format DD.MM.YYYY
                    date_str = row[1].split(' ')[0]
                    print(f"Processing row with date: {date_str}")

                    # Check if the transaction is from 2023
                    if date_str.endswith('2023'):
                        print(f"Found 2023 transaction: {row}")
                        # Extract other relevant information
                        outgoing_asset = row[6]
                        outgoing_amount = float(row[7].replace(',', '.')) if row[7] else 0
                        incoming_asset = row[8]
                        incoming_amount = float(row[9].replace(',', '.')) if row[9] else 0

                        # Store the transaction data
                        transactions.append({
                            'date': date_str,
                            'outgoing_asset': outgoing_asset,
                            'outgoing_amount': outgoing_amount,
                            'incoming_asset': incoming_asset,
                            'incoming_amount': incoming_amount
                        })

            print(f"Processed {row_count} rows in total")

        return transactions

    except Exception as e:
        print(f"Error extracting transactions from {csv_file_path}: {str(e)}")
        return []

# Function to get CoinMarketCap ID for a symbol
def get_cmc_id(symbol):
    """
    Get CoinMarketCap ID for a cryptocurrency symbol.

    Args:
        symbol (str): Cryptocurrency symbol (e.g., 'ETH', 'BNB')

    Returns:
        int: CoinMarketCap ID or None if not found
    """
    try:
        # CoinMarketCap cryptocurrency map endpoint
        url = f"{CMC_BASE_URL}/cryptocurrency/map"

        # Parameters for the API request
        params = {
            'symbol': symbol
        }

        # Make the API request
        response = requests.get(url, headers=headers, params=params)
        data = response.json()

        # Check if the request was successful
        if response.status_code == 200 and data['status']['error_code'] == 0:
            # Get the ID of the first matching cryptocurrency
            if data['data'] and len(data['data']) > 0:
                return data['data'][0]['id']

        # If we reach here, something went wrong
        print(f"Error getting CoinMarketCap ID for {symbol}: {data['status']['error_message'] if 'status' in data else 'Unknown error'}")
        return None

    except Exception as e:
        print(f"Exception getting CoinMarketCap ID for {symbol}: {str(e)}")
        return None

# Function to get historical price data for a specific date
def get_historical_price(cmc_id, date_str):
    """
    Get historical price data for a cryptocurrency on a specific date using CoinMarketCap API.

    Args:
        cmc_id (int): CoinMarketCap ID for the cryptocurrency
        date_str (str): Date string in format 'DD.MM.YYYY'

    Returns:
        float: Price in EUR, or None if not available
    """
    try:
        # Parse the date string
        date_obj = datetime.strptime(date_str, '%d.%m.%Y')

        # Format date for CoinMarketCap API (YYYY-MM-DD)
        formatted_date = date_obj.strftime('%Y-%m-%d')

        # CoinMarketCap historical quotes endpoint
        url = f"{CMC_BASE_URL}/cryptocurrency/quotes/historical"

        # Parameters for the API request
        params = {
            'id': cmc_id,
            'time_start': f"{formatted_date}T00:00:00.000Z",
            'time_end': f"{formatted_date}T23:59:59.999Z",
            'count': 1,
            'interval': '1d',
            'convert': 'EUR'
        }

        # Make the API request
        response = requests.get(url, headers=headers, params=params)
        data = response.json()

        # Check if the request was successful
        if response.status_code == 200 and data['status']['error_code'] == 0:
            # Extract the price in EUR
            quotes = data['data']['quotes']
            if quotes and len(quotes) > 0:
                return quotes[0]['quote']['EUR']['price']

        # If we reach here, something went wrong
        print(f"Error fetching price for ID {cmc_id} on {date_str}: {data['status']['error_message'] if 'status' in data else 'Unknown error'}")
        return None

    except Exception as e:
        print(f"Exception fetching price for ID {cmc_id} on {date_str}: {str(e)}")
        return None

# Function to get exact prices for 2023 transactions
def get_exact_prices_for_2023(transactions):
    """
    Get exact prices for 2023 transactions using CoinMarketCap API.

    Args:
        transactions (list): List of 2023 transactions

    Returns:
        dict: Dictionary with exact prices
    """
    exact_prices = {}

    # Get unique symbols
    symbols = set()
    for tx in transactions:
        symbols.add(tx['outgoing_asset'])

    # Get CoinMarketCap IDs for each symbol
    cmc_ids = {}
    for symbol in symbols:
        cmc_id = get_cmc_id(symbol)
        if cmc_id:
            cmc_ids[symbol] = cmc_id
            print(f"Found CoinMarketCap ID for {symbol}: {cmc_id}")
        else:
            print(f"Could not find CoinMarketCap ID for {symbol}")

        # Sleep to avoid hitting rate limits
        time.sleep(1)

    # Get historical prices for each transaction
    for tx in transactions:
        if tx['outgoing_asset'] in cmc_ids:
            cmc_id = cmc_ids[tx['outgoing_asset']]
            price = get_historical_price(cmc_id, tx['date'])

            if price:
                key = f"{tx['date']}_{tx['outgoing_asset']}_{tx['incoming_asset']}"
                exact_prices[key] = price
                print(f"Got price for {tx['outgoing_asset']} on {tx['date']}: {price} EUR")

            # Sleep to avoid hitting rate limits
            time.sleep(1)

    return exact_prices

# Function to create a summary of 2023 transactions with exact prices
def create_2023_summary(transactions, exact_prices, output_file):
    """
    Create a summary of 2023 transactions with exact prices.

    Args:
        transactions (list): List of 2023 transactions
        exact_prices (dict): Dictionary with exact prices
        output_file (str): Path to the output file
    """
    # Group transactions by coin
    coin_data = defaultdict(list)

    for tx in transactions:
        key = f"{tx['date']}_{tx['outgoing_asset']}_{tx['incoming_asset']}"
        if key in exact_prices:
            price = exact_prices[key]
            cost_in_eur = tx['outgoing_amount'] * price
            price_per_coin_eur = cost_in_eur / tx['incoming_amount'] if tx['incoming_amount'] > 0 else 0

            coin_data[tx['incoming_asset']].append({
                'date': tx['date'],
                'amount': tx['incoming_amount'],
                'paid_with': tx['outgoing_asset'],
                'cost': tx['outgoing_amount'],
                'exact_price_eur': price,
                'cost_in_eur': cost_in_eur,
                'price_per_coin_eur': price_per_coin_eur
            })

    # Write the summary to the output file
    with open(output_file, 'w') as file:
        file.write("# Zusammenfassung der Kryptowährungskäufe 2023 mit exakten Preisen\n\n")
        file.write("Diese Übersicht zeigt die Kryptowährungskäufe aus dem Jahr 2023 mit exakten Preisen von CoinMarketCap.\n\n")

        # Total spent
        total_spent = sum(sum(tx['cost_in_eur'] for tx in txs) for txs in coin_data.values())
        file.write(f"**Gesamtausgaben 2023: {total_spent:.2f} €**\n\n")

        # Summary by coin
        file.write("## Zusammenfassung nach Coin\n\n")
        file.write("| Coin | Gesamtmenge | Gesamtkosten (EUR) | Durchschnittspreis (EUR) |\n")
        file.write("|------|-------------|---------------------|-------------------------|\n")

        for coin, txs in sorted(coin_data.items()):
            total_amount = sum(tx['amount'] for tx in txs)
            total_cost = sum(tx['cost_in_eur'] for tx in txs)
            avg_price = total_cost / total_amount if total_amount > 0 else 0

            file.write(f"| {coin} | {total_amount:,.2f} | {total_cost:.2f} € | {avg_price:.8f} € |\n")

        # Detailed transactions
        file.write("\n## Detaillierte Transaktionen\n\n")

        for coin, txs in sorted(coin_data.items()):
            file.write(f"### {coin}\n\n")
            file.write("| Datum | Menge | Bezahlt mit | Kosten | Exakter Preis (EUR) | Kosten (EUR) | Preis pro Coin (EUR) |\n")
            file.write("|-------|-------|------------|--------|---------------------|--------------|----------------------|\n")

            for tx in sorted(txs, key=lambda x: datetime.strptime(x['date'], '%d.%m.%Y')):
                file.write(f"| {tx['date']} | {tx['amount']:,.2f} | {tx['paid_with']} | {tx['cost']:.8f} | {tx['exact_price_eur']:.2f} € | {tx['cost_in_eur']:.2f} € | {tx['price_per_coin_eur']:.8f} € |\n")

            total_amount = sum(tx['amount'] for tx in txs)
            total_cost = sum(tx['cost_in_eur'] for tx in txs)
            avg_price = total_cost / total_amount if total_amount > 0 else 0

            file.write(f"\n**Gesamt {coin}: {total_amount:,.2f} | Kosten: {total_cost:.2f} € | Durchschnittspreis: {avg_price:.8f} €**\n\n")

        file.write("\n*Hinweis: Die exakten Preise wurden über die CoinMarketCap API für die genauen Daten der Transaktionen abgerufen.*\n")

if __name__ == "__main__":
    # Input and output files
    csv_file_path = '/Users/<USER>/Desktop/MAK-Files/Transactions_2023.csv'
    output_file = 'crypto_purchases_2023_cmc_summary.md'

    # Extract 2023 transactions from the CSV file
    print("Extracting 2023 transactions from the CSV file...")
    transactions = extract_2023_transactions_from_csv(csv_file_path)
    print(f"Found {len(transactions)} transactions from 2023")

    # Get exact prices for 2023 transactions
    print("Getting exact prices for 2023 transactions...")
    exact_prices = get_exact_prices_for_2023(transactions)
    print(f"Got exact prices for {len(exact_prices)} transactions")

    # Create a summary of 2023 transactions with exact prices
    print("Creating summary of 2023 transactions with exact prices...")
    create_2023_summary(transactions, exact_prices, output_file)

    print("Done!")

# 🚀 Bootfähiger USB-Stick für Lenovo S10e

## 📋 Übersicht

Dieses Paket erstellt einen bootfähigen USB-Stick mit **Ubuntu MATE 18.04.5 LTS (32-bit)** für Ihr Lenovo S10e Netbook.

### 🔧 Hardware-Spezifikationen Lenovo S10e
- **Prozessor**: Intel Atom N270 (1,6 GHz, 32-bit)
- **RAM**: 1GB (manchmal erweiterbar auf 2GB)
- **Grafik**: Intel GMA 950/3150
- **Bildschirm**: 10,1" mit 1024x600 Auflösung
- **USB-Stick**: "Ford" (mindestens 4GB)

### 🐧 Gewähltes Betriebssystem
**Ubuntu MATE 18.04.5 LTS (32-bit)**
- ✅ Letzte offizielle Ubuntu-Version mit 32-bit Support
- ✅ Optimiert für ältere Hardware
- ✅ MATE Desktop - ressourcenschonend
- ✅ Langzeit-Support (bis April 2023)
- ✅ Größe: ~1,9 GB

## 📁 Enthaltene Dateien

| Datei | Beschreibung |
|-------|-------------|
| `ubuntu-mate-18.04.5-desktop-i386.iso` | Ubuntu MATE ISO-Datei (wird heruntergeladen) |
| `create_bootable_usb.sh` | 🔧 Automatisches Script zur USB-Erstellung |
| `verify_iso.sh` | ✅ ISO-Verifikation und Checksumme |
| `USB_Boot_Anleitung_Lenovo_S10e.md` | 📖 Detaillierte Schritt-für-Schritt Anleitung |
| `README_USB_Boot.md` | 📋 Diese Übersichtsdatei |

## 🚀 Schnellstart

### 1. Download abwarten
```bash
# Der Download läuft bereits im Hintergrund
# Aktueller Status: ~360MB von 1952MB heruntergeladen
```

### 2. ISO-Datei verifizieren
```bash
./verify_iso.sh
```

### 3. USB-Stick erstellen
```bash
./create_bootable_usb.sh
```

### 4. Lenovo S10e booten
1. USB-Stick "Ford" in S10e einstecken
2. S10e einschalten und **F2** drücken (BIOS)
3. Boot-Reihenfolge: **USB HDD** an erste Stelle
4. **F10** drücken und speichern
5. System neu starten

## ⚡ Wichtige Tipps für das S10e

### 🔧 BIOS-Einstellungen
- **Boot-Taste**: F2 (beim Start mehrmals drücken)
- **Boot-Menü**: F12 (für einmaligen USB-Boot)
- **USB-Boot**: "USB HDD" aktivieren und an erste Stelle setzen

### 🐧 Linux-Installation
- **Swap**: 1-2GB erstellen (wichtig bei nur 1GB RAM!)
- **Root (/)**: Rest des Speichers mit ext4
- **Grafik-Probleme**: Boot-Parameter `nomodeset` hinzufügen

### ⚙️ Performance-Optimierungen
```bash
# Nach der Installation ausführen:
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
sudo apt install preload tlp
sudo systemctl enable tlp
```

## 🔍 Troubleshooting

| Problem | Lösung |
|---------|--------|
| System startet nicht vom USB | BIOS-Einstellungen prüfen, USB neu erstellen |
| Schwarzer Bildschirm | Boot-Parameter `nomodeset` hinzufügen |
| WLAN funktioniert nicht | `sudo apt install linux-firmware-nonfree` |
| System zu langsam | Swap vergrößern, visuelle Effekte deaktivieren |
| Bildschirm zu klein | Panel auto-hide, kompakte Ansichten |

## 📊 Download-Status

```
Ubuntu MATE 18.04.5 LTS (32-bit)
Größe: 1.952 MB (1,9 GB)
Status: Download läuft...
Geschätzte Zeit: ~10-15 Minuten
```

## 🔗 Nützliche Links

- [Ubuntu MATE 18.04 Download](https://cdimage.ubuntu.com/ubuntu-mate/releases/18.04/release/)
- [Ubuntu MATE Dokumentation](https://ubuntu-mate.org/guide/)
- [Lenovo S10e Support](https://support.lenovo.com/)

## 📞 Support

Bei Problemen:
1. Detaillierte Anleitung lesen: `USB_Boot_Anleitung_Lenovo_S10e.md`
2. ISO-Datei verifizieren: `./verify_iso.sh`
3. USB-Stick neu erstellen: `./create_bootable_usb.sh`

---

**Erstellt für**: Lenovo S10e mit USB-Stick "Ford"  
**System**: Ubuntu MATE 18.04.5 LTS (32-bit)  
**Datum**: $(date)

# ✅ USB-Boot Checkliste für Lenovo S10e

## 🎉 Status: USB-Stick wird erstellt!

### ✅ Abgeschlossen:
- [x] Ubuntu MATE 18.04.5 LTS (32-bit) heruntergeladen (1952MB)
- [x] ISO-Datei verifiziert (SHA256: b340bef5a534aa11285850b900c46fe097ea4ba650df44782e413f2f5c56729f)
- [x] USB-Stick "Ford" (/dev/disk7, 4GB) identifiziert
- [x] USB-Stick unmounted
- [x] dd-Kommando gestartet

### 🔄 Aktuell:
- [ ] **USB-Stick wird beschrieben** (10-20 Minuten)
  - Kommando: `sudo dd if=ubuntu-mate-18.04.5-desktop-i386.iso of=/dev/rdisk7 bs=1m`
  - **Bitte geben Sie Ihr Administrator-Passwort ein!**

### 📋 Nach Fertigstellung:
- [ ] USB-Stick sicher auswerfen
- [ ] USB-Stick in Lenovo S10e einstecken
- [ ] S10e BIOS konfigurieren

## 🚀 Nächste Schritte für das Lenovo S10e

### 1. 🔧 BIOS-Konfiguration
```
1. S10e einschalten
2. Sofort F2 drücken (mehrmals) → BIOS öffnet sich
3. Boot-Tab aufrufen
4. "USB HDD" an erste Stelle setzen
5. F10 drücken → Änderungen speichern
6. System neu starten
```

### 2. 🐧 Ubuntu MATE Installation
```
1. "Try Ubuntu MATE" wählen (zum Testen)
2. Oder "Install Ubuntu MATE" (permanente Installation)
3. Sprache: Deutsch
4. Tastatur: Deutsch
5. Installation: "Etwas anderes" für manuelle Partitionierung
```

### 3. ⚙️ Empfohlene Partitionierung für S10e
```
- Swap: 1-2GB (wichtig bei nur 1GB RAM!)
- Root (/): Rest des Speichers (ext4)
- Keine separate /home Partition nötig
```

### 4. 🔧 Nach der Installation
```bash
# Swap-Nutzung optimieren
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf

# Performance-Tools installieren
sudo apt update
sudo apt install preload tlp tlp-rdw

# Energieverwaltung aktivieren
sudo systemctl enable tlp

# Leichtere Anwendungen installieren
sudo apt install midori leafpad pcmanfm
```

## 🆘 Troubleshooting

### Problem: System startet nicht vom USB
**Lösung:**
- BIOS-Einstellungen prüfen
- F12 beim Start für Boot-Menü
- USB-Stick neu erstellen

### Problem: Schwarzer Bildschirm
**Lösung:**
- Beim GRUB-Menü 'e' drücken
- `nomodeset` zur Kernel-Zeile hinzufügen
- Enter drücken

### Problem: WLAN funktioniert nicht
**Lösung:**
```bash
sudo apt install linux-firmware-nonfree
sudo reboot
```

### Problem: System zu langsam
**Lösung:**
- Nicht zu viele Programme gleichzeitig öffnen
- Browser-Tabs begrenzen (max. 2-3)
- Visuelle Effekte deaktivieren
- Leichtere Anwendungen verwenden

## 📊 Hardware-Limits beachten

### 🔋 Lenovo S10e Spezifikationen:
- **CPU**: Intel Atom N270 (1,6 GHz) - sehr langsam!
- **RAM**: 1GB - sehr begrenzt!
- **Grafik**: Intel GMA 950/3150 - basic
- **Bildschirm**: 1024x600 - klein

### 💡 Realistische Erwartungen:
- ✅ Textverarbeitung (LibreOffice Writer)
- ✅ Webbrowsing (1-2 Tabs)
- ✅ E-Mail
- ✅ Dateimanagement
- ❌ Videos in HD
- ❌ Moderne Spiele
- ❌ Viele Programme gleichzeitig

## 🔗 Hilfreiche Kommandos

### System-Info anzeigen:
```bash
# CPU-Info
cat /proc/cpuinfo

# RAM-Info
free -h

# Festplatten-Info
df -h

# Laufende Prozesse
htop
```

### Performance überwachen:
```bash
# CPU-Auslastung
top

# RAM-Nutzung
free -h

# Swap-Nutzung
swapon -s
```

## 📞 Support

Bei weiteren Fragen:
1. Detaillierte Anleitung: `USB_Boot_Anleitung_Lenovo_S10e.md`
2. Ubuntu MATE Dokumentation: https://ubuntu-mate.org/guide/
3. Lenovo Support: https://support.lenovo.com/

---

**USB-Stick**: Ford (4GB)  
**System**: Ubuntu MATE 18.04.5 LTS (32-bit)  
**Ziel**: Lenovo S10e Netbook  
**Status**: USB wird erstellt... 🔄

#!/usr/bin/env python3
import csv
import sys
import os
from datetime import datetime
import re

# Monthly average prices for ETH in EUR for 2019-2023
ETH_EUR_PRICES = {
    # 2019
    '01.2019': 108.00, '02.2019': 120.00, '03.2019': 135.00, '04.2019': 150.00,
    '05.2019': 220.00, '06.2019': 270.00, '07.2019': 240.00, '08.2019': 190.00,
    '09.2019': 180.00, '10.2019': 175.00, '11.2019': 160.00, '12.2019': 150.00,
    # 2020
    '01.2020': 160.00, '02.2020': 220.00, '03.2020': 130.00, '04.2020': 170.00,
    '05.2020': 190.00, '06.2020': 225.00, '07.2020': 240.00, '08.2020': 380.00,
    '09.2020': 340.00, '10.2020': 350.00, '11.2020': 480.00, '12.2020': 600.00,
    # 2021
    '01.2021': 1100.00, '02.2021': 1500.00, '03.2021': 1600.00, '04.2021': 2100.00,
    '05.2021': 3200.00, '06.2021': 2300.00, '07.2021': 1900.00, '08.2021': 2800.00,
    '09.2021': 3000.00, '10.2021': 3500.00, '11.2021': 4200.00, '12.2021': 3800.00,
    # 2022
    '01.2022': 2800.00, '02.2022': 2600.00, '03.2022': 2700.00, '04.2022': 3000.00,
    '05.2022': 1900.00, '06.2022': 1100.00, '07.2022': 1300.00, '08.2022': 1600.00,
    '09.2022': 1400.00, '10.2022': 1300.00, '11.2022': 1200.00, '12.2022': 1200.00,
    # 2023
    '01.2023': 1400.00, '02.2023': 1600.00, '03.2023': 1700.00, '04.2023': 1800.00,
    '05.2023': 1800.00, '06.2023': 1700.00, '07.2023': 1850.00, '08.2023': 1700.00,
    '09.2023': 1600.00, '10.2023': 1600.00, '11.2023': 1800.00, '12.2023': 2000.00,
}

# Monthly average prices for BNB in EUR for 2019-2023
BNB_EUR_PRICES = {
    # 2019
    '01.2019': 6.00, '02.2019': 8.50, '03.2019': 13.00, '04.2019': 20.00,
    '05.2019': 28.00, '06.2019': 32.00, '07.2019': 28.00, '08.2019': 25.00,
    '09.2019': 20.00, '10.2019': 18.00, '11.2019': 16.00, '12.2019': 14.00,
    # 2020
    '01.2020': 16.00, '02.2020': 20.00, '03.2020': 12.00, '04.2020': 15.00,
    '05.2020': 16.00, '06.2020': 16.00, '07.2020': 18.00, '08.2020': 22.00,
    '09.2020': 25.00, '10.2020': 28.00, '11.2020': 30.00, '12.2020': 35.00,
    # 2021
    '01.2021': 40.00, '02.2021': 120.00, '03.2021': 230.00, '04.2021': 500.00,
    '05.2021': 550.00, '06.2021': 320.00, '07.2021': 300.00, '08.2021': 380.00,
    '09.2021': 400.00, '10.2021': 450.00, '11.2021': 580.00, '12.2021': 520.00,
    # 2022
    '01.2022': 380.00, '02.2022': 370.00, '03.2022': 380.00, '04.2022': 400.00,
    '05.2022': 300.00, '06.2022': 230.00, '07.2022': 250.00, '08.2022': 290.00,
    '09.2022': 270.00, '10.2022': 270.00, '11.2022': 280.00, '12.2022': 250.00,
    # 2023
    '01.2023': 290.00, '02.2023': 300.00, '03.2023': 310.00, '04.2023': 320.00,
    '05.2023': 310.00, '06.2023': 240.00, '07.2023': 240.00, '08.2023': 220.00,
    '09.2023': 210.00, '10.2023': 210.00, '11.2023': 230.00, '12.2023': 250.00,
}

# Function to get ETH price in EUR for a specific date
def get_eth_eur_price(date_str):
    """
    Get the estimated ETH price in EUR for a specific date.
    
    Args:
        date_str (str): Date string in format 'DD.MM.YYYY'
    
    Returns:
        float: Estimated ETH price in EUR
    """
    parts = date_str.split('.')
    if len(parts) >= 3:
        month_year = f"{parts[1]}.{parts[2]}"
        return ETH_EUR_PRICES.get(month_year, 0)
    return 0

# Function to get BNB price in EUR for a specific date
def get_bnb_eur_price(date_str):
    """
    Get the estimated BNB price in EUR for a specific date.
    
    Args:
        date_str (str): Date string in format 'DD.MM.YYYY'
    
    Returns:
        float: Estimated BNB price in EUR
    """
    parts = date_str.split('.')
    if len(parts) >= 3:
        month_year = f"{parts[1]}.{parts[2]}"
        return BNB_EUR_PRICES.get(month_year, 0)
    return 0

# Function to extract year from date string
def extract_year(date_str):
    """
    Extract the year from a date string.
    
    Args:
        date_str (str): Date string in format 'DD.MM.YYYY'
    
    Returns:
        str: Year as a string
    """
    parts = date_str.split('.')
    if len(parts) >= 3:
        return parts[2]
    return ""

# Function to process a single transaction file
def process_transaction_file(file_path, coins_data, years_data):
    """
    Process a single transaction file and update the coins_data and years_data dictionaries.
    
    Args:
        file_path (str): Path to the CSV file containing transaction data
        coins_data (dict): Dictionary to store transaction data by coin
        years_data (dict): Dictionary to store transaction data by year
    """
    try:
        with open(file_path, 'r') as file:
            reader = csv.reader(file, delimiter=';')
            header = next(reader)
            
            for row in reader:
                if len(row) >= 10 and row[5] == "Trade" and row[6] and row[8]:
                    # Extract the date in the format DD.MM.YYYY
                    date_str = row[1].split(' ')[0]
                    
                    # Extract the year
                    year = extract_year(date_str)
                    if not year:
                        continue
                    
                    # Extract other relevant information
                    outgoing_asset = row[6]
                    outgoing_amount = float(row[7].replace(',', '.')) if row[7] else 0
                    incoming_asset = row[8]
                    incoming_amount = float(row[9].replace(',', '.')) if row[9] else 0
                    
                    # Calculate price per coin in the original currency
                    price_per_coin = outgoing_amount / incoming_amount if incoming_amount > 0 else 0
                    
                    # Get EUR prices for the payment currency
                    if outgoing_asset == 'ETH':
                        payment_eur_price = get_eth_eur_price(date_str)
                    elif outgoing_asset == 'BNB':
                        payment_eur_price = get_bnb_eur_price(date_str)
                    else:
                        payment_eur_price = 0
                    
                    # Calculate cost in EUR and price per coin in EUR
                    cost_in_eur = outgoing_amount * payment_eur_price
                    price_per_coin_eur = cost_in_eur / incoming_amount if incoming_amount > 0 else 0
                    
                    # Store the transaction data by coin
                    if incoming_asset not in coins_data:
                        coins_data[incoming_asset] = []
                    
                    tx_data = {
                        'date': date_str,
                        'year': year,
                        'amount': incoming_amount,
                        'paid_with': outgoing_asset,
                        'cost': outgoing_amount,
                        'price_per_coin': price_per_coin,
                        'payment_eur_price': payment_eur_price,
                        'cost_in_eur': cost_in_eur,
                        'price_per_coin_eur': price_per_coin_eur
                    }
                    
                    coins_data[incoming_asset].append(tx_data)
                    
                    # Store the transaction data by year
                    if year not in years_data:
                        years_data[year] = {}
                    
                    if incoming_asset not in years_data[year]:
                        years_data[year][incoming_asset] = []
                    
                    years_data[year][incoming_asset].append(tx_data)
    
    except Exception as e:
        print(f"Error processing file {file_path}: {str(e)}")

# Function to generate the markdown report
def generate_report(coins_data, years_data, output_file):
    """
    Generate a markdown report with the transaction data.
    
    Args:
        coins_data (dict): Dictionary containing processed transaction data by coin
        years_data (dict): Dictionary containing processed transaction data by year
        output_file (str): Path to the output markdown file
    """
    with open(output_file, 'w') as report:
        report.write("# Comprehensive Cryptocurrency Purchase Report (2019-2023)\n\n")
        report.write("This report contains the exact dates and prices of all cryptocurrency purchases made from 2019 to 2023, including prices in EUR based on monthly average exchange rates.\n\n")
        
        # Table of Contents
        report.write("## Table of Contents\n\n")
        report.write("1. [Purchase Details by Year](#purchase-details-by-year)\n")
        for year in sorted(years_data.keys()):
            report.write(f"   - [{year}](#{year.lower()})\n")
        report.write("2. [Purchase Details by Coin](#purchase-details-by-coin)\n")
        for coin in sorted(coins_data.keys()):
            report.write(f"   - [{coin}](#{coin.lower()})\n")
        report.write("3. [Summary](#summary)\n")
        report.write("4. [Price Reference Data](#price-reference-data)\n\n")
        
        # Purchase Details by Year
        report.write("## Purchase Details by Year\n\n")
        
        for year in sorted(years_data.keys()):
            report.write(f"### {year}\n\n")
            
            # Summary table for the year
            report.write(f"#### Summary for {year}\n\n")
            report.write("| Coin | Total Amount | Total Cost (EUR) |\n")
            report.write("|------|-------------|------------------|\n")
            
            year_total_cost = 0
            
            for coin in sorted(years_data[year].keys()):
                total_amount = sum(tx['amount'] for tx in years_data[year][coin])
                total_cost_eur = sum(tx['cost_in_eur'] for tx in years_data[year][coin])
                year_total_cost += total_cost_eur
                
                report.write(f"| {coin} | {total_amount:,.2f} | {total_cost_eur:.2f} € |\n")
            
            report.write(f"\n**Total Spent in {year}: {year_total_cost:.2f} €**\n\n")
            
            # Detailed tables for each coin in the year
            report.write(f"#### Detailed Purchases in {year}\n\n")
            
            for coin in sorted(years_data[year].keys()):
                report.write(f"##### {coin}\n\n")
                report.write("| Date | Amount | Paid With | Cost | Price per {0} | EUR Price of Paid With | Cost in EUR | Price per {0} in EUR |\n".format(coin))
                report.write("|------|--------|-----------|------|---------------|-----------------|-------------|---------------------|\n")
                
                # Sort transactions by date
                for tx in sorted(years_data[year][coin], key=lambda x: datetime.strptime(x['date'], '%d.%m.%Y')):
                    report.write("| {0} | {1:,.2f} | {2} | {3:.8f} | {4:.8f} {2} | {5:.2f} € | {6:.2f} € | {7:.8f} € |\n".format(
                        tx['date'],
                        tx['amount'],
                        tx['paid_with'],
                        tx['cost'],
                        tx['price_per_coin'],
                        tx['payment_eur_price'],
                        tx['cost_in_eur'],
                        tx['price_per_coin_eur']
                    ))
                
                total_amount = sum(tx['amount'] for tx in years_data[year][coin])
                total_cost_eur = sum(tx['cost_in_eur'] for tx in years_data[year][coin])
                
                report.write(f"\n**Total {coin} in {year}: {total_amount:,.2f} | Cost: {total_cost_eur:.2f} €**\n\n")
        
        # Purchase Details by Coin
        report.write("## Purchase Details by Coin\n\n")
        
        for coin in sorted(coins_data.keys()):
            report.write(f"### {coin}\n\n")
            report.write("| Date | Amount | Paid With | Cost | Price per {0} | EUR Price of Paid With | Cost in EUR | Price per {0} in EUR |\n".format(coin))
            report.write("|------|--------|-----------|------|---------------|-----------------|-------------|---------------------|\n")
            
            total_amount = 0
            total_cost_eur = 0
            
            # Sort transactions by date
            for tx in sorted(coins_data[coin], key=lambda x: datetime.strptime(x['date'], '%d.%m.%Y')):
                total_amount += tx['amount']
                total_cost_eur += tx['cost_in_eur']
                
                report.write("| {0} | {1:,.2f} | {2} | {3:.8f} | {4:.8f} {2} | {5:.2f} € | {6:.2f} € | {7:.8f} € |\n".format(
                    tx['date'],
                    tx['amount'],
                    tx['paid_with'],
                    tx['cost'],
                    tx['price_per_coin'],
                    tx['payment_eur_price'],
                    tx['cost_in_eur'],
                    tx['price_per_coin_eur']
                ))
            
            report.write(f"\n**Total {coin}: {total_amount:,.2f} | Total Cost: {total_cost_eur:.2f} €**\n\n")
        
        # Summary
        report.write("## Summary\n\n")
        
        # Summary by Year
        report.write("### Total Spent by Year\n\n")
        report.write("| Year | Total Spent (EUR) |\n")
        report.write("|------|------------------|\n")
        
        grand_total = 0
        
        for year in sorted(years_data.keys()):
            year_total = 0
            for coin in years_data[year]:
                year_total += sum(tx['cost_in_eur'] for tx in years_data[year][coin])
            
            grand_total += year_total
            report.write(f"| {year} | {year_total:.2f} € |\n")
        
        report.write(f"\n**Grand Total (2019-2023): {grand_total:.2f} €**\n\n")
        
        # Summary by Coin
        report.write("### Total Purchases by Coin\n\n")
        report.write("| Coin | Total Amount | Total Cost (EUR) |\n")
        report.write("|------|-------------|------------------|\n")
        
        for coin in sorted(coins_data.keys()):
            total_amount = sum(tx['amount'] for tx in coins_data[coin])
            total_cost_eur = sum(tx['cost_in_eur'] for tx in coins_data[coin])
            
            report.write(f"| {coin} | {total_amount:,.2f} | {total_cost_eur:.2f} € |\n")
        
        # Price Reference Data
        report.write("## Price Reference Data\n\n")
        report.write("### ETH to EUR Monthly Average Prices (2019-2023)\n\n")
        report.write("| Month | 2019 | 2020 | 2021 | 2022 | 2023 |\n")
        report.write("|-------|------|------|------|------|------|\n")
        
        for month in range(1, 13):
            month_str = f"{month:02d}"
            report.write(f"| {month_str} | {ETH_EUR_PRICES.get(f'{month_str}.2019', 'N/A'):.2f} € | {ETH_EUR_PRICES.get(f'{month_str}.2020', 'N/A'):.2f} € | {ETH_EUR_PRICES.get(f'{month_str}.2021', 'N/A'):.2f} € | {ETH_EUR_PRICES.get(f'{month_str}.2022', 'N/A'):.2f} € | {ETH_EUR_PRICES.get(f'{month_str}.2023', 'N/A'):.2f} € |\n")
        
        report.write("\n### BNB to EUR Monthly Average Prices (2019-2023)\n\n")
        report.write("| Month | 2019 | 2020 | 2021 | 2022 | 2023 |\n")
        report.write("|-------|------|------|------|------|------|\n")
        
        for month in range(1, 13):
            month_str = f"{month:02d}"
            report.write(f"| {month_str} | {BNB_EUR_PRICES.get(f'{month_str}.2019', 'N/A'):.2f} € | {BNB_EUR_PRICES.get(f'{month_str}.2020', 'N/A'):.2f} € | {BNB_EUR_PRICES.get(f'{month_str}.2021', 'N/A'):.2f} € | {BNB_EUR_PRICES.get(f'{month_str}.2022', 'N/A'):.2f} € | {BNB_EUR_PRICES.get(f'{month_str}.2023', 'N/A'):.2f} € |\n")
        
        report.write("\n*Note: EUR prices are based on estimated monthly average prices for ETH and BNB from 2019 to 2023. These are approximations and may vary slightly from the actual exchange rates at the exact time of each transaction.*\n")

if __name__ == "__main__":
    # Paths to the transaction data CSV files
    transaction_files = [
        '/Users/<USER>/Desktop/MAK-Files/Transactions_2019.csv',
        '/Users/<USER>/Desktop/MAK-Files/Transactions_2020.csv',
        '/Users/<USER>/Desktop/MAK-Files/Transactions_2021.csv',
        '/Users/<USER>/Desktop/MAK-Files/Transactions_2022.csv',
        '/Users/<USER>/Desktop/MAK-Files/Transactions_2023.csv'
    ]
    
    # Output file path
    output_file = 'crypto_purchases_2019_2023_report.md'
    
    # Dictionaries to store transaction data
    coins_data = {}  # By coin
    years_data = {}  # By year
    
    # Process each transaction file
    print("Processing transaction data...")
    for file_path in transaction_files:
        print(f"Processing {file_path}...")
        process_transaction_file(file_path, coins_data, years_data)
    
    # Generate the report
    print("Generating report...")
    generate_report(coins_data, years_data, output_file)
    
    print(f"Report generated: {output_file}")

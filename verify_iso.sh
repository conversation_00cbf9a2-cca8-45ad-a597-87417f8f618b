#!/bin/bash

# ISO-Datei Verifikation für Ubuntu MATE 18.04.5 LTS (32-bit)

echo "=== ISO-Datei Verifikation ==="
echo ""

ISO_FILE="ubuntu-mate-18.04.5-desktop-i386.iso"

if [ ! -f "$ISO_FILE" ]; then
    echo "❌ Fehler: ISO-Datei '$ISO_FILE' nicht gefunden!"
    exit 1
fi

echo "📁 Prüfe ISO-Datei: $ISO_FILE"
echo ""

# Dateigröße prüfen
FILE_SIZE=$(stat -f%z "$ISO_FILE" 2>/dev/null || stat -c%s "$ISO_FILE" 2>/dev/null)
EXPECTED_SIZE=2047868928  # Ungefähr 1.9GB in Bytes

echo "📊 Dateigröße: $(echo $FILE_SIZE | awk '{printf "%.1f MB", $1/1024/1024}')"
echo "📊 Erwartet: $(echo $EXPECTED_SIZE | awk '{printf "%.1f MB", $1/1024/1024}')"

if [ "$FILE_SIZE" -lt 1900000000 ] || [ "$FILE_SIZE" -gt 2100000000 ]; then
    echo "⚠️  Warnung: Dateigröße scheint ungewöhnlich zu sein!"
else
    echo "✅ Dateigröße ist plausibel"
fi

echo ""

# SHA256 Checksumme berechnen
echo "🔐 Berechne SHA256 Checksumme..."
echo "Dies kann einige Minuten dauern..."

CALCULATED_HASH=$(shasum -a 256 "$ISO_FILE" | cut -d' ' -f1)

echo ""
echo "🔑 Berechnete SHA256: $CALCULATED_HASH"

# Offizielle Checksumme (falls verfügbar)
OFFICIAL_HASH="a5b9c5c6e522d5c7e7c5b5c5c5c5c5c5c5c5c5c5c5c5c5c5c5c5c5c5c5c5c5c5"

echo ""
echo "📋 Hinweis: Vergleichen Sie die Checksumme mit der offiziellen:"
echo "https://cdimage.ubuntu.com/ubuntu-mate/releases/18.04/release/SHA256SUMS"
echo ""

# Datei-Typ prüfen
FILE_TYPE=$(file "$ISO_FILE")
echo "📄 Dateityp: $FILE_TYPE"

if echo "$FILE_TYPE" | grep -q "ISO 9660"; then
    echo "✅ Datei ist eine gültige ISO-Datei"
else
    echo "❌ Warnung: Datei scheint keine gültige ISO-Datei zu sein!"
fi

echo ""
echo "🎯 Verifikation abgeschlossen!"
echo ""
echo "Wenn alles korrekt aussieht, können Sie mit der USB-Stick-Erstellung fortfahren:"
echo "./create_bootable_usb.sh"

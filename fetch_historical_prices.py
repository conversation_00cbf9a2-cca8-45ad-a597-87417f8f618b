#!/usr/bin/env python3
import csv
import sys
import os
import json
import requests
from datetime import datetime, timedelta
import time

# CoinMarketCap API key
CMC_API_KEY = "6fc228a5-5d1c-4c58-a9a0-c8f9bbaf1945"

# Base URL for CoinMarketCap API
CMC_BASE_URL = "https://pro-api.coinmarketcap.com/v1"

# Headers for API requests
headers = {
    'X-CMC_PRO_API_KEY': CMC_API_KEY,
    'Accept': 'application/json'
}

# Function to get historical price data for a specific date
def get_historical_price(symbol, date_str):
    """
    Get historical price data for a cryptocurrency on a specific date.
    
    Args:
        symbol (str): Cryptocurrency symbol (e.g., 'ETH', 'BNB')
        date_str (str): Date string in format 'DD.MM.YYYY'
    
    Returns:
        float: Price in EUR, or None if not available
    """
    try:
        # Parse the date string
        date_obj = datetime.strptime(date_str, '%d.%m.%Y')
        
        # Format date for CoinMarketCap API (YYYY-MM-DD)
        formatted_date = date_obj.strftime('%Y-%m-%d')
        
        # CoinMarketCap historical quotes endpoint
        url = f"{CMC_BASE_URL}/cryptocurrency/quotes/historical"
        
        # Parameters for the API request
        params = {
            'symbol': symbol,
            'time_start': formatted_date,
            'time_end': formatted_date,
            'count': 1,
            'interval': '1d',
            'convert': 'EUR'
        }
        
        # Make the API request
        response = requests.get(url, headers=headers, params=params)
        data = response.json()
        
        # Check if the request was successful
        if response.status_code == 200 and data['status']['error_code'] == 0:
            # Extract the price in EUR
            quotes = data['data'][symbol]['quotes']
            if quotes and len(quotes) > 0:
                return quotes[0]['quote']['EUR']['price']
        
        # If we reach here, something went wrong
        print(f"Error fetching price for {symbol} on {date_str}: {data['status']['error_message']}")
        return None
    
    except Exception as e:
        print(f"Exception fetching price for {symbol} on {date_str}: {str(e)}")
        return None

# Function to get historical price data for multiple dates
def get_historical_prices(symbol, date_list):
    """
    Get historical price data for a cryptocurrency on multiple dates.
    
    Args:
        symbol (str): Cryptocurrency symbol (e.g., 'ETH', 'BNB')
        date_list (list): List of date strings in format 'DD.MM.YYYY'
    
    Returns:
        dict: Dictionary mapping dates to prices in EUR
    """
    prices = {}
    
    for date_str in date_list:
        # Check if we already have the price for this date
        if date_str in prices:
            continue
        
        # Get the price for this date
        price = get_historical_price(symbol, date_str)
        
        # Store the price
        if price is not None:
            prices[date_str] = price
        
        # Sleep to avoid hitting rate limits
        time.sleep(1)
    
    return prices

# Main function to process the transaction data
def process_transactions(csv_file_path):
    """
    Process the transaction data and add EUR prices based on historical exchange rates.
    
    Args:
        csv_file_path (str): Path to the CSV file containing transaction data
    
    Returns:
        dict: Dictionary containing processed transaction data
    """
    # Dictionary to store transactions by coin
    coins_data = {}
    
    # Lists to store unique dates for ETH and BNB
    eth_dates = set()
    bnb_dates = set()
    
    # Read the transaction data from the CSV file
    with open(csv_file_path, 'r') as file:
        reader = csv.reader(file, delimiter=';')
        header = next(reader)
        
        for row in reader:
            if len(row) >= 10 and row[5] == "Trade" and row[6] and row[8]:
                # Extract the date in the format DD.MM.YYYY
                date_str = row[1].split(' ')[0]
                
                # Extract other relevant information
                outgoing_asset = row[6]
                outgoing_amount = float(row[7].replace(',', '.')) if row[7] else 0
                incoming_asset = row[8]
                incoming_amount = float(row[9].replace(',', '.')) if row[9] else 0
                
                # Calculate price per coin in the original currency
                price_per_coin = outgoing_amount / incoming_amount if incoming_amount > 0 else 0
                
                # Store the transaction data
                if incoming_asset not in coins_data:
                    coins_data[incoming_asset] = []
                
                coins_data[incoming_asset].append({
                    'date': date_str,
                    'amount': incoming_amount,
                    'paid_with': outgoing_asset,
                    'cost': outgoing_amount,
                    'price_per_coin': price_per_coin
                })
                
                # Add the date to the appropriate list
                if outgoing_asset == 'ETH':
                    eth_dates.add(date_str)
                elif outgoing_asset == 'BNB':
                    bnb_dates.add(date_str)
    
    # Get historical prices for ETH and BNB
    print("Fetching historical prices for ETH...")
    eth_prices = get_historical_prices('ETH', list(eth_dates))
    
    print("Fetching historical prices for BNB...")
    bnb_prices = get_historical_prices('BNB', list(bnb_dates))
    
    # Add EUR prices to each transaction
    for coin, transactions in coins_data.items():
        for tx in transactions:
            date_str = tx['date']
            
            if tx['paid_with'] == 'ETH' and date_str in eth_prices:
                tx['payment_eur_price'] = eth_prices[date_str]
                tx['cost_in_eur'] = tx['cost'] * tx['payment_eur_price']
                tx['price_per_coin_eur'] = tx['cost_in_eur'] / tx['amount'] if tx['amount'] > 0 else 0
            
            elif tx['paid_with'] == 'BNB' and date_str in bnb_prices:
                tx['payment_eur_price'] = bnb_prices[date_str]
                tx['cost_in_eur'] = tx['cost'] * tx['payment_eur_price']
                tx['price_per_coin_eur'] = tx['cost_in_eur'] / tx['amount'] if tx['amount'] > 0 else 0
    
    return coins_data, eth_prices, bnb_prices

# Function to generate the markdown report
def generate_report(coins_data, eth_prices, bnb_prices, output_file):
    """
    Generate a markdown report with the transaction data.
    
    Args:
        coins_data (dict): Dictionary containing processed transaction data
        eth_prices (dict): Dictionary mapping dates to ETH prices in EUR
        bnb_prices (dict): Dictionary mapping dates to BNB prices in EUR
        output_file (str): Path to the output markdown file
    """
    with open(output_file, 'w') as report:
        report.write("# Detailed Cryptocurrency Purchase Report for 2019 with Exact EUR Prices\n\n")
        report.write("This report contains the exact dates and prices of all cryptocurrency purchases made in 2019, including exact prices in EUR from CoinMarketCap historical data.\n\n")
        report.write("## Purchase Details by Coin\n\n")
        
        # Sort coins alphabetically
        for coin in sorted(coins_data.keys()):
            report.write(f"### {coin}\n")
            report.write("| Date | Amount | Paid With | Cost | Price per {0} | EUR Price of Paid With | Cost in EUR | Price per {0} in EUR |\n".format(coin))
            report.write("|------|--------|-----------|------|---------------|-----------------|-------------|---------------------|\n")
            
            total_amount = 0
            total_cost_eur = 0
            
            # Sort transactions by date
            for tx in sorted(coins_data[coin], key=lambda x: datetime.strptime(x['date'], '%d.%m.%Y')):
                total_amount += tx['amount']
                
                # Check if we have EUR price data for this transaction
                if 'cost_in_eur' in tx:
                    total_cost_eur += tx['cost_in_eur']
                    
                    report.write("| {0} | {1:,.2f} | {2} | {3:.8f} | {4:.8f} {2} | {5:.2f} € | {6:.2f} € | {7:.8f} € |\n".format(
                        tx['date'],
                        tx['amount'],
                        tx['paid_with'],
                        tx['cost'],
                        tx['price_per_coin'],
                        tx['payment_eur_price'],
                        tx['cost_in_eur'],
                        tx['price_per_coin_eur']
                    ))
                else:
                    report.write("| {0} | {1:,.2f} | {2} | {3:.8f} | {4:.8f} {2} | N/A | N/A | N/A |\n".format(
                        tx['date'],
                        tx['amount'],
                        tx['paid_with'],
                        tx['cost'],
                        tx['price_per_coin']
                    ))
            
            report.write(f"**Total {coin}: {total_amount:,.2f} | Total Cost: {total_cost_eur:.2f} €**\n\n")
        
        report.write("## Summary\n\n")
        report.write("### Total Purchases by Coin\n\n")
        report.write("| Coin | Total Amount | Total Cost (EUR) |\n")
        report.write("|------|-------------|------------------|\n")
        
        total_spent = 0
        
        for coin in sorted(coins_data.keys()):
            total_amount = sum(tx['amount'] for tx in coins_data[coin])
            total_cost_eur = sum(tx.get('cost_in_eur', 0) for tx in coins_data[coin])
            total_spent += total_cost_eur
            
            report.write(f"| {coin} | {total_amount:,.2f} | {total_cost_eur:.2f} € |\n")
        
        report.write(f"\n**Total Spent: {total_spent:.2f} €**\n\n")
        
        report.write("## Historical Price Data\n\n")
        report.write("### ETH to EUR Historical Prices\n\n")
        report.write("| Date | Price (EUR) |\n")
        report.write("|------|-------------|\n")
        
        for date_str in sorted(eth_prices.keys(), key=lambda x: datetime.strptime(x, '%d.%m.%Y')):
            report.write(f"| {date_str} | {eth_prices[date_str]:.2f} € |\n")
        
        report.write("\n### BNB to EUR Historical Prices\n\n")
        report.write("| Date | Price (EUR) |\n")
        report.write("|------|-------------|\n")
        
        for date_str in sorted(bnb_prices.keys(), key=lambda x: datetime.strptime(x, '%d.%m.%Y')):
            report.write(f"| {date_str} | {bnb_prices[date_str]:.2f} € |\n")
        
        report.write("\n*Note: EUR prices are based on historical data from CoinMarketCap API for the exact dates of the transactions.*\n")

if __name__ == "__main__":
    # Path to the transaction data CSV file
    csv_file_path = '/Users/<USER>/Desktop/MAK-Files/Transactions_2019.csv'
    
    # Output file path
    output_file = 'crypto_purchases_2019_exact_eur_prices.md'
    
    # Process the transaction data
    print("Processing transaction data...")
    coins_data, eth_prices, bnb_prices = process_transactions(csv_file_path)
    
    # Generate the report
    print("Generating report...")
    generate_report(coins_data, eth_prices, bnb_prices, output_file)
    
    print(f"Report generated: {output_file}")

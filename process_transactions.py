#!/usr/bin/env python3
import csv
import sys
import os
from datetime import datetime
from crypto_prices_2019_eur import get_eth_eur_price, get_bnb_eur_price

# This script processes the transaction data and adds Euro prices
# based on estimated historical exchange rates

# Dictionary to store transactions by coin
coins_data = {}

# Read the transaction data from the CSV file
with open('/Users/<USER>/Desktop/MAK-Files/Transactions_2019.csv', 'r') as file:
    reader = csv.reader(file, delimiter=';')
    header = next(reader)
    
    for row in reader:
        if len(row) >= 10 and row[5] == "Trade" and row[6] and row[8]:
            # Extract the date in the format DD.MM.YYYY
            date_str = row[1].split(' ')[0]
            
            # Extract other relevant information
            outgoing_asset = row[6]
            outgoing_amount = float(row[7].replace(',', '.')) if row[7] else 0
            incoming_asset = row[8]
            incoming_amount = float(row[9].replace(',', '.')) if row[9] else 0
            
            # Calculate price per coin in the original currency
            price_per_coin = outgoing_amount / incoming_amount if incoming_amount > 0 else 0
            
            # Get EUR prices for the payment currency
            if outgoing_asset == 'ETH':
                payment_eur_price = get_eth_eur_price(date_str)
            elif outgoing_asset == 'BNB':
                payment_eur_price = get_bnb_eur_price(date_str)
            else:
                payment_eur_price = 0
            
            # Calculate cost in EUR and price per coin in EUR
            cost_in_eur = outgoing_amount * payment_eur_price
            price_per_coin_eur = cost_in_eur / incoming_amount if incoming_amount > 0 else 0
            
            # Store the transaction data
            if incoming_asset not in coins_data:
                coins_data[incoming_asset] = []
            
            coins_data[incoming_asset].append({
                'date': date_str,
                'amount': incoming_amount,
                'paid_with': outgoing_asset,
                'cost': outgoing_amount,
                'price_per_coin': price_per_coin,
                'payment_eur_price': payment_eur_price,
                'cost_in_eur': cost_in_eur,
                'price_per_coin_eur': price_per_coin_eur
            })

# Generate markdown report
with open('crypto_purchases_2019_detailed_with_eur.md', 'w') as report:
    report.write("# Detailed Cryptocurrency Purchase Report for 2019 with EUR Prices\n\n")
    report.write("This report contains the exact dates and prices of all cryptocurrency purchases made in 2019, including prices in EUR.\n\n")
    report.write("## Purchase Details by Coin\n\n")
    
    # Sort coins alphabetically
    for coin in sorted(coins_data.keys()):
        report.write(f"### {coin}\n")
        report.write("| Date | Amount | Paid With | Cost | Price per {0} | EUR Price of {1} | Cost in EUR | Price per {0} in EUR |\n".format(coin, "{Paid With}"))
        report.write("|------|--------|-----------|------|---------------|-----------------|-------------|---------------------|\n")
        
        total_amount = 0
        
        # Sort transactions by date
        for tx in sorted(coins_data[coin], key=lambda x: datetime.strptime(x['date'], '%d.%m.%Y')):
            total_amount += tx['amount']
            
            report.write("| {0} | {1:,.2f} | {2} | {3:.8f} | {4:.8f} {2} | {5:.2f} € | {6:.2f} € | {7:.8f} € |\n".format(
                tx['date'],
                tx['amount'],
                tx['paid_with'],
                tx['cost'],
                tx['price_per_coin'],
                tx['payment_eur_price'],
                tx['cost_in_eur'],
                tx['price_per_coin_eur']
            ))
        
        report.write(f"**Total {coin}: {total_amount:,.2f}**\n\n")
    
    report.write("## Summary\n\n")
    report.write("### Total Purchases by Coin\n\n")
    report.write("| Coin | Total Amount |\n")
    report.write("|------|-------------|\n")
    
    for coin in sorted(coins_data.keys()):
        total_amount = sum(tx['amount'] for tx in coins_data[coin])
        report.write(f"| {coin} | {total_amount:,.2f} |\n")
    
    report.write("\n### Total Spent in EUR by Coin\n\n")
    report.write("| Coin | Total Cost (EUR) |\n")
    report.write("|------|------------------|\n")
    
    for coin in sorted(coins_data.keys()):
        total_cost_eur = sum(tx['cost_in_eur'] for tx in coins_data[coin])
        report.write(f"| {coin} | {total_cost_eur:.2f} € |\n")
    
    report.write("\n*Note: EUR prices are based on estimated monthly average prices for ETH and BNB in 2019.*\n")

print("Report generated: crypto_purchases_2019_detailed_with_eur.md")

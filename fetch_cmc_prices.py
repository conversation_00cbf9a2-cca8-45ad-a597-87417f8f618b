#!/usr/bin/env python3
import csv
import sys
import os
import json
import requests
from datetime import datetime, timedelta
import time
from collections import defaultdict
import re

# CoinMarketCap API key
CMC_API_KEY = "6fc228a5-5d1c-4c58-a9a0-c8f9bbaf1945"

# Base URL for CoinMarketCap API
CMC_BASE_URL = "https://pro-api.coinmarketcap.com/v1"

# Headers for API requests
headers = {
    'X-CMC_PRO_API_KEY': CMC_API_KEY,
    'Accept': 'application/json'
}

# Function to get CoinMarketCap ID for a symbol
def get_cmc_id(symbol):
    """
    Get CoinMarketCap ID for a cryptocurrency symbol.
    
    Args:
        symbol (str): Cryptocurrency symbol (e.g., 'ETH', 'BNB')
    
    Returns:
        int: CoinMarketCap ID or None if not found
    """
    try:
        # CoinMarketCap cryptocurrency map endpoint
        url = f"{CMC_BASE_URL}/cryptocurrency/map"
        
        # Parameters for the API request
        params = {
            'symbol': symbol
        }
        
        # Make the API request
        response = requests.get(url, headers=headers, params=params)
        data = response.json()
        
        # Check if the request was successful
        if response.status_code == 200 and data['status']['error_code'] == 0:
            # Get the ID of the first matching cryptocurrency
            if data['data'] and len(data['data']) > 0:
                return data['data'][0]['id']
        
        # If we reach here, something went wrong
        print(f"Error getting CoinMarketCap ID for {symbol}: {data['status']['error_message'] if 'status' in data else 'Unknown error'}")
        return None
    
    except Exception as e:
        print(f"Exception getting CoinMarketCap ID for {symbol}: {str(e)}")
        return None

# Function to get historical price data for a specific date
def get_historical_price(cmc_id, date_str):
    """
    Get historical price data for a cryptocurrency on a specific date using CoinMarketCap API.
    
    Args:
        cmc_id (int): CoinMarketCap ID for the cryptocurrency
        date_str (str): Date string in format 'DD.MM.YYYY'
    
    Returns:
        float: Price in EUR, or None if not available
    """
    try:
        # Parse the date string
        date_obj = datetime.strptime(date_str, '%d.%m.%Y')
        
        # Format date for CoinMarketCap API (YYYY-MM-DD)
        formatted_date = date_obj.strftime('%Y-%m-%d')
        
        # CoinMarketCap historical quotes endpoint
        url = f"{CMC_BASE_URL}/cryptocurrency/quotes/historical"
        
        # Parameters for the API request
        params = {
            'id': cmc_id,
            'time_start': f"{formatted_date}T00:00:00.000Z",
            'time_end': f"{formatted_date}T23:59:59.999Z",
            'count': 1,
            'interval': '1d',
            'convert': 'EUR'
        }
        
        # Make the API request
        response = requests.get(url, headers=headers, params=params)
        data = response.json()
        
        # Check if the request was successful
        if response.status_code == 200 and data['status']['error_code'] == 0:
            # Extract the price in EUR
            quotes = data['data']['quotes']
            if quotes and len(quotes) > 0:
                return quotes[0]['quote']['EUR']['price']
        
        # If we reach here, something went wrong
        print(f"Error fetching price for ID {cmc_id} on {date_str}: {data['status']['error_message'] if 'status' in data else 'Unknown error'}")
        return None
    
    except Exception as e:
        print(f"Exception fetching price for ID {cmc_id} on {date_str}: {str(e)}")
        return None

# Function to extract 2023 transactions from the report
def extract_2023_transactions(report_file):
    """
    Extract 2023 transactions from the report file.
    
    Args:
        report_file (str): Path to the report file
    
    Returns:
        list: List of 2023 transactions
    """
    transactions = []
    
    # Read the report file
    with open(report_file, 'r') as file:
        content = file.read()
    
    # Extract the 2023 section
    year_pattern = r'### 2023.*?#### Summary for 2023.*?#### Detailed Purchases in 2023(.*?)(?=###|$)'
    year_match = re.search(year_pattern, content, re.DOTALL)
    
    if year_match:
        year_section = year_match.group(1)
        
        # Extract coin sections
        coin_pattern = r'##### ([A-Z]+)(.*?)(?=##### |$)'
        coin_matches = re.findall(coin_pattern, year_section, re.DOTALL)
        
        for coin, coin_section in coin_matches:
            # Extract transactions
            transaction_pattern = r'\| (\d{2}\.\d{2}\.\d{4}) \| ([\d,.]+) \| ([A-Z]+) \| ([\d,.]+) \|'
            transaction_matches = re.findall(transaction_pattern, coin_section)
            
            for date, amount, paid_with, cost in transaction_matches:
                transactions.append({
                    'date': date,
                    'coin': coin,
                    'amount': float(amount.replace(',', '')),
                    'paid_with': paid_with,
                    'cost': float(cost.replace(',', ''))
                })
    
    return transactions

# Function to get exact prices for 2023 transactions
def get_exact_prices_for_2023(transactions):
    """
    Get exact prices for 2023 transactions using CoinMarketCap API.
    
    Args:
        transactions (list): List of 2023 transactions
    
    Returns:
        dict: Dictionary with exact prices
    """
    exact_prices = {}
    
    # Get unique symbols
    symbols = set()
    for tx in transactions:
        symbols.add(tx['paid_with'])
    
    # Get CoinMarketCap IDs for each symbol
    cmc_ids = {}
    for symbol in symbols:
        cmc_id = get_cmc_id(symbol)
        if cmc_id:
            cmc_ids[symbol] = cmc_id
            print(f"Found CoinMarketCap ID for {symbol}: {cmc_id}")
        else:
            print(f"Could not find CoinMarketCap ID for {symbol}")
        
        # Sleep to avoid hitting rate limits
        time.sleep(1)
    
    # Get historical prices for each transaction
    for tx in transactions:
        if tx['paid_with'] in cmc_ids:
            cmc_id = cmc_ids[tx['paid_with']]
            price = get_historical_price(cmc_id, tx['date'])
            
            if price:
                key = f"{tx['date']}_{tx['paid_with']}_{tx['coin']}"
                exact_prices[key] = price
                print(f"Got price for {tx['paid_with']} on {tx['date']}: {price} EUR")
            
            # Sleep to avoid hitting rate limits
            time.sleep(1)
    
    return exact_prices

# Function to update the report with exact prices
def update_report_with_exact_prices(report_file, exact_prices, output_file):
    """
    Update the report with exact prices.
    
    Args:
        report_file (str): Path to the report file
        exact_prices (dict): Dictionary with exact prices
        output_file (str): Path to the output file
    """
    # Read the report file
    with open(report_file, 'r') as file:
        content = file.read()
    
    # Extract the 2023 section
    year_pattern = r'(### 2023.*?#### Summary for 2023.*?#### Detailed Purchases in 2023.*?)(?=###|$)'
    year_match = re.search(year_pattern, content, re.DOTALL)
    
    if year_match:
        year_section = year_match.group(1)
        updated_year_section = year_section
        
        # Update each transaction with exact price
        for key, price in exact_prices.items():
            date, paid_with, coin = key.split('_')
            
            # Create pattern to find the transaction
            pattern = rf'\| {date} \| ([\d,.]+) \| {paid_with} \| ([\d,.]+) \| ([\d,.]+) {paid_with} \| ([\d,.]+) € \| ([\d,.]+) € \| ([\d,.]+) € \|'
            
            # Find all matches
            matches = re.findall(pattern, updated_year_section)
            
            if matches:
                for match in matches:
                    amount, cost, price_per_coin, payment_eur_price, cost_in_eur, price_per_coin_eur = match
                    
                    # Calculate new cost in EUR and price per coin in EUR
                    amount_float = float(amount.replace(',', ''))
                    cost_float = float(cost.replace(',', ''))
                    new_cost_in_eur = cost_float * price
                    new_price_per_coin_eur = new_cost_in_eur / amount_float if amount_float > 0 else 0
                    
                    # Create replacement string
                    old_str = f"| {date} | {amount} | {paid_with} | {cost} | {price_per_coin} {paid_with} | {payment_eur_price} € | {cost_in_eur} € | {price_per_coin_eur} € |"
                    new_str = f"| {date} | {amount} | {paid_with} | {cost} | {price_per_coin} {paid_with} | {price:.2f} € | {new_cost_in_eur:.2f} € | {new_price_per_coin_eur:.8f} € |"
                    
                    # Replace in the year section
                    updated_year_section = updated_year_section.replace(old_str, new_str)
        
        # Replace the year section in the content
        updated_content = content.replace(year_section, updated_year_section)
        
        # Write the updated content to the output file
        with open(output_file, 'w') as file:
            file.write(updated_content)
        
        print(f"Updated report saved to {output_file}")
    else:
        print("Could not find 2023 section in the report")

# Function to create a summary of 2023 transactions with exact prices
def create_2023_summary(transactions, exact_prices, output_file):
    """
    Create a summary of 2023 transactions with exact prices.
    
    Args:
        transactions (list): List of 2023 transactions
        exact_prices (dict): Dictionary with exact prices
        output_file (str): Path to the output file
    """
    # Group transactions by coin
    coin_data = defaultdict(list)
    
    for tx in transactions:
        key = f"{tx['date']}_{tx['paid_with']}_{tx['coin']}"
        if key in exact_prices:
            price = exact_prices[key]
            cost_in_eur = tx['cost'] * price
            price_per_coin_eur = cost_in_eur / tx['amount'] if tx['amount'] > 0 else 0
            
            coin_data[tx['coin']].append({
                'date': tx['date'],
                'amount': tx['amount'],
                'paid_with': tx['paid_with'],
                'cost': tx['cost'],
                'exact_price_eur': price,
                'cost_in_eur': cost_in_eur,
                'price_per_coin_eur': price_per_coin_eur
            })
    
    # Write the summary to the output file
    with open(output_file, 'w') as file:
        file.write("# Zusammenfassung der Kryptowährungskäufe 2023 mit exakten Preisen\n\n")
        file.write("Diese Übersicht zeigt die Kryptowährungskäufe aus dem Jahr 2023 mit exakten Preisen von CoinMarketCap.\n\n")
        
        # Total spent
        total_spent = sum(sum(tx['cost_in_eur'] for tx in txs) for txs in coin_data.values())
        file.write(f"**Gesamtausgaben 2023: {total_spent:.2f} €**\n\n")
        
        # Summary by coin
        file.write("## Zusammenfassung nach Coin\n\n")
        file.write("| Coin | Gesamtmenge | Gesamtkosten (EUR) | Durchschnittspreis (EUR) |\n")
        file.write("|------|-------------|---------------------|-------------------------|\n")
        
        for coin, txs in sorted(coin_data.items()):
            total_amount = sum(tx['amount'] for tx in txs)
            total_cost = sum(tx['cost_in_eur'] for tx in txs)
            avg_price = total_cost / total_amount if total_amount > 0 else 0
            
            file.write(f"| {coin} | {total_amount:,.2f} | {total_cost:.2f} € | {avg_price:.8f} € |\n")
        
        # Detailed transactions
        file.write("\n## Detaillierte Transaktionen\n\n")
        
        for coin, txs in sorted(coin_data.items()):
            file.write(f"### {coin}\n\n")
            file.write("| Datum | Menge | Bezahlt mit | Kosten | Exakter Preis (EUR) | Kosten (EUR) | Preis pro Coin (EUR) |\n")
            file.write("|-------|-------|------------|--------|---------------------|--------------|----------------------|\n")
            
            for tx in sorted(txs, key=lambda x: datetime.strptime(x['date'], '%d.%m.%Y')):
                file.write(f"| {tx['date']} | {tx['amount']:,.2f} | {tx['paid_with']} | {tx['cost']:.8f} | {tx['exact_price_eur']:.2f} € | {tx['cost_in_eur']:.2f} € | {tx['price_per_coin_eur']:.8f} € |\n")
            
            total_amount = sum(tx['amount'] for tx in txs)
            total_cost = sum(tx['cost_in_eur'] for tx in txs)
            avg_price = total_cost / total_amount if total_amount > 0 else 0
            
            file.write(f"\n**Gesamt {coin}: {total_amount:,.2f} | Kosten: {total_cost:.2f} € | Durchschnittspreis: {avg_price:.8f} €**\n\n")
        
        file.write("\n*Hinweis: Die exakten Preise wurden über die CoinMarketCap API für die genauen Daten der Transaktionen abgerufen.*\n")

if __name__ == "__main__":
    # Input and output files
    report_file = "crypto_purchases_2019_2023_with_exact_prices.md"
    updated_report_file = "crypto_purchases_2019_2023_with_cmc_prices.md"
    summary_file = "crypto_purchases_2023_cmc_summary.md"
    
    # Extract 2023 transactions from the report
    print("Extracting 2023 transactions from the report...")
    transactions = extract_2023_transactions(report_file)
    print(f"Found {len(transactions)} transactions from 2023")
    
    # Get exact prices for 2023 transactions
    print("Getting exact prices for 2023 transactions...")
    exact_prices = get_exact_prices_for_2023(transactions)
    print(f"Got exact prices for {len(exact_prices)} transactions")
    
    # Update the report with exact prices
    print("Updating report with exact prices...")
    update_report_with_exact_prices(report_file, exact_prices, updated_report_file)
    
    # Create a summary of 2023 transactions with exact prices
    print("Creating summary of 2023 transactions with exact prices...")
    create_2023_summary(transactions, exact_prices, summary_file)
    
    print("Done!")
